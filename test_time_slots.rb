#!/usr/bin/env ruby

# Test script to check faculty time slots API
require 'net/http'
require 'json'
require 'uri'

# Test the faculty time slots endpoint
uri = URI('http://localhost:3000/consultation_requests/faculty/1/time_slots')
uri.query = URI.encode_www_form({
  start_date: '2025-07-21',
  end_date: '2025-07-27'
})

begin
  response = Net::HTTP.get_response(uri)
  
  puts "Status: #{response.code}"
  puts "Response:"
  
  if response.code == '200'
    data = JSON.parse(response.body)
    puts JSON.pretty_generate(data)
  else
    puts response.body
  end
rescue => e
  puts "Error: #{e.message}"
end
