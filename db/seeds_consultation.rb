# Consultation System Seed Data
puts "Creating consultation system seed data..."

# Create a simple test without requiring Rails environment
# This will be run with: rails runner db/seeds_consultation.rb

# Find or create a faculty user
faculty_user = User.where(email: '<EMAIL>').first

if faculty_user.nil?
  puts "Creating faculty user..."
  faculty_user = User.create!(
    name: "Dr. <PERSON>",
    email: "<EMAIL>",
    workflow_state: 'active'
  )
end

puts "Faculty user: #{faculty_user.name} (ID: #{faculty_user.id})"

# We need to ensure this user has a teacher enrollment
# Find or create a course
course = Course.where(name: 'Test Course for Consultation').first
if course.nil?
  course = Course.create!(
    name: 'Test Course for Consultation',
    workflow_state: 'available'
  )
  puts "Created course: #{course.name} (ID: #{course.id})"
end

# Create teacher enrollment if it doesn't exist
enrollment = course.teacher_enrollments.where(user: faculty_user).first
if enrollment.nil?
  enrollment = course.teacher_enrollments.create!(
    user: faculty_user,
    workflow_state: 'active'
  )
  puts "Created teacher enrollment for faculty user"
end

# Clear existing time slots
faculty_user.faculty_time_slots.destroy_all

# Create recurring time slots
puts "Creating recurring time slots..."

%w[Monday Tuesday Wednesday Thursday Friday].each do |day|
  # Morning slot: 9:00 AM - 10:00 AM
  faculty_user.faculty_time_slots.create!(
    day_of_week: day,
    start_time: Time.parse("09:00"),
    end_time: Time.parse("10:00"),
    is_recurring: true,
    is_available: true,
    notes: "Morning consultation"
  )

  # Afternoon slot: 2:00 PM - 3:00 PM
  faculty_user.faculty_time_slots.create!(
    day_of_week: day,
    start_time: Time.parse("14:00"),
    end_time: Time.parse("15:00"),
    is_recurring: true,
    is_available: true,
    notes: "Afternoon consultation"
  )
end

puts "Created #{faculty_user.faculty_time_slots.count} time slots"

# Also create some specific date slots for this week
puts "Creating specific date slots for this week..."
(Date.today..Date.today + 6.days).each do |date|
  next if date.wday == 0 || date.wday == 6 # Skip weekends

  faculty_user.faculty_time_slots.create!(
    day_of_week: date.strftime('%A'),
    start_time: Time.parse("11:00"),
    end_time: Time.parse("12:00"),
    specific_date: date,
    is_recurring: false,
    is_available: true,
    notes: "Special consultation for #{date}"
  )
end

puts "Total time slots: #{faculty_user.faculty_time_slots.count}"
puts "Available slots: #{faculty_user.faculty_time_slots.available.count}"
puts "Recurring slots: #{faculty_user.faculty_time_slots.recurring.count}"
puts "Specific date slots: #{faculty_user.faculty_time_slots.specific_date.count}"

# Test the available_faculty_for_consultation method
puts "\nTesting available faculty method..."
test_student = User.joins(:student_enrollments).where(enrollments: { workflow_state: 'active' }).first
if test_student
  available_faculty = test_student.available_faculty_for_consultation
  puts "Available faculty for consultation: #{available_faculty.count}"
  available_faculty.each do |faculty|
    puts "  - #{faculty.name} (ID: #{faculty.id})"
  end
else
  puts "No student users found to test with"
end

puts "Seed data creation completed!"
