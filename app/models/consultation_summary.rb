# frozen_string_literal: true

class ConsultationSummary < ActiveRecord::Base
  belongs_to :consultation_request
  belongs_to :faculty, class_name: 'User'
  belongs_to :student, class_name: 'User'

  CONCERN_TYPES = ConsultationRequest::NATURE_OF_CONCERNS

  validates :student_name, :student_number, :consultation_date, :concern_type, presence: true
  validates :student_name, length: { maximum: 255 }
  validates :student_number, length: { maximum: 50 }
  validates :concern_type, inclusion: { in: CONCERN_TYPES }
  validates :faculty_notes, :outcome_summary, :follow_up_required, length: { maximum: 2000 }, allow_blank: true
  validates :referral_made, length: { maximum: 255 }, allow_blank: true

  scope :for_faculty, ->(faculty) { where(faculty: faculty) }
  scope :for_student, ->(student) { where(student: student) }
  scope :by_concern_type, ->(concern_type) { where(concern_type: concern_type) }
  scope :in_date_range, ->(start_date, end_date) { 
    where(consultation_date: start_date.beginning_of_day..end_date.end_of_day) 
  }
  scope :recent, -> { order(consultation_date: :desc) }
  scope :with_referrals, -> { where.not(referral_made: [nil, '']) }
  scope :requiring_follow_up, -> { where.not(follow_up_required: [nil, '']) }

  # Search functionality
  scope :search_by_student, ->(query) {
    where('student_name ILIKE ? OR student_number ILIKE ?', "%#{query}%", "%#{query}%")
  }

  scope :search_by_content, ->(query) {
    where('description ILIKE ? OR faculty_notes ILIKE ? OR outcome_summary ILIKE ?', 
          "%#{query}%", "%#{query}%", "%#{query}%")
  }

  # Reporting methods
  def self.grouped_by_concern_type_for_faculty(faculty_user)
    for_faculty(faculty_user)
      .group(:concern_type)
      .count
      .transform_keys(&:humanize)
  end

  def self.monthly_summary_for_faculty(faculty_user, year = Date.current.year)
    for_faculty(faculty_user)
      .where(consultation_date: Date.new(year).beginning_of_year..Date.new(year).end_of_year)
      .group_by_month(:consultation_date)
      .group(:concern_type)
      .count
  end

  def self.statistics_for_faculty(faculty_user, start_date = 1.year.ago, end_date = Date.current)
    summaries = for_faculty(faculty_user).in_date_range(start_date, end_date)
    
    {
      total_consultations: summaries.count,
      by_concern_type: summaries.group(:concern_type).count,
      with_referrals: summaries.with_referrals.count,
      requiring_follow_up: summaries.requiring_follow_up.count,
      average_per_month: summaries.count / ((end_date - start_date.to_date) / 30).ceil
    }
  end

  # Instance methods
  def concern_type_display
    concern_type.humanize
  end

  def formatted_consultation_date
    consultation_date.strftime('%B %d, %Y at %I:%M %p')
  end

  def has_referral?
    referral_made.present?
  end

  def requires_follow_up?
    follow_up_required.present?
  end

  def duration_display
    "30 minutes" # Default consultation duration
  end

  # Export functionality
  def to_csv_row
    [
      student_name,
      student_number,
      formatted_consultation_date,
      concern_type_display,
      description,
      faculty_notes,
      outcome_summary,
      referral_made,
      follow_up_required,
      created_at.strftime('%Y-%m-%d')
    ]
  end

  def self.csv_headers
    [
      'Student Name',
      'Student ID',
      'Consultation Date',
      'Concern Type',
      'Description',
      'Faculty Notes',
      'Outcome Summary',
      'Referral Made',
      'Follow-up Required',
      'Record Created'
    ]
  end

  def self.to_csv(summaries)
    require 'csv'
    
    CSV.generate(headers: true) do |csv|
      csv << csv_headers
      summaries.each do |summary|
        csv << summary.to_csv_row
      end
    end
  end

  # Update methods for faculty
  def update_outcome!(outcome_summary, referral_made = nil, follow_up_required = nil)
    update!(
      outcome_summary: outcome_summary,
      referral_made: referral_made,
      follow_up_required: follow_up_required
    )
  end

  def add_faculty_notes!(notes)
    current_notes = faculty_notes.present? ? "#{faculty_notes}\n\n" : ""
    timestamp = Time.current.strftime('%Y-%m-%d %H:%M')
    new_notes = "#{current_notes}[#{timestamp}] #{notes}"
    
    update!(faculty_notes: new_notes)
  end

  private

  # Validation helpers
  def consultation_date_not_in_future
    return unless consultation_date

    if consultation_date > Time.current
      errors.add(:consultation_date, 'cannot be in the future')
    end
  end
end
