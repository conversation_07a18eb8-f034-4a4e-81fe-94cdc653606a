# frozen_string_literal: true

class ConsultationR<PERSON><PERSON><PERSON><PERSON>
  def self.perform
    Rails.logger.info "Starting consultation reminder job"

    begin
      ConsultationNotificationService.schedule_consultation_reminders
      Rails.logger.info "Consultation reminder job completed successfully"
    rescue => e
      Rails.logger.error "Consultation reminder job failed: #{e.message}"
      raise e
    end
  end
end
