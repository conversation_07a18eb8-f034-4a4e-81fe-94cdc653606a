# frozen_string_literal: true

class ConsultationCalendarService
  def self.create_calendar_event_for_consultation(consultation_request)
    return unless consultation_request.approved?

    # Find or create a calendar event for this consultation
    existing_event = find_existing_calendar_event(consultation_request)
    return existing_event if existing_event

    # Create new calendar event
    create_new_calendar_event(consultation_request)
  end

  def self.update_calendar_event_for_consultation(consultation_request)
    event = find_existing_calendar_event(consultation_request)
    return unless event

    case consultation_request.status
    when 'completed'
      update_event_as_completed(event, consultation_request)
    when 'cancelled', 'declined'
      cancel_calendar_event(event, consultation_request)
    else
      update_event_details(event, consultation_request)
    end
  end

  def self.cancel_calendar_event_for_consultation(consultation_request)
    event = find_existing_calendar_event(consultation_request)
    return unless event

    cancel_calendar_event(event, consultation_request)
  end

  def self.check_for_conflicts(faculty_user, start_time, end_time)
    # Check for existing calendar events that would conflict
    conflicts = CalendarEvent.active
                             .where(context: faculty_user)
                             .where('start_at < ? AND end_at > ?', end_time, start_time)

    # Also check for other approved consultations
    consultation_conflicts = ConsultationRequest.approved
                                               .joins(:faculty)
                                               .where(faculty: faculty_user)
                                               .where('preferred_datetime < ? AND preferred_datetime + INTERVAL 30 MINUTE > ?', 
                                                      end_time, start_time)

    {
      calendar_conflicts: conflicts,
      consultation_conflicts: consultation_conflicts,
      has_conflicts: conflicts.exists? || consultation_conflicts.exists?
    }
  end

  private

  def self.find_existing_calendar_event(consultation_request)
    CalendarEvent.active
                 .where(context: consultation_request.faculty)
                 .where("title LIKE ? OR description LIKE ?", 
                        "%Consultation%#{consultation_request.student_name}%",
                        "%consultation_request_id:#{consultation_request.id}%")
                 .where(start_at: consultation_request.preferred_datetime.beginning_of_hour..consultation_request.preferred_datetime.end_of_hour)
                 .first
  end

  def self.create_new_calendar_event(consultation_request)
    start_time = consultation_request.preferred_datetime
    end_time = start_time + 30.minutes

    event_params = {
      title: "Consultation: #{consultation_request.student_name}",
      description: build_event_description(consultation_request),
      start_at: start_time,
      end_at: end_time,
      location_name: "Faculty Office / Online",
      context: consultation_request.faculty,
      workflow_state: 'active'
    }

    begin
      event = CalendarEvent.create!(event_params)
      
      # Store the consultation request ID in the event for future reference
      event.update_column(:comments, "consultation_request_id:#{consultation_request.id}")
      
      Rails.logger.info "Created calendar event #{event.id} for consultation request #{consultation_request.id}"
      event
    rescue ActiveRecord::RecordInvalid => e
      Rails.logger.error "Failed to create calendar event for consultation request #{consultation_request.id}: #{e.message}"
      nil
    end
  end

  def self.update_event_details(event, consultation_request)
    event.update!(
      title: "Consultation: #{consultation_request.student_name}",
      description: build_event_description(consultation_request),
      start_at: consultation_request.preferred_datetime,
      end_at: consultation_request.preferred_datetime + 30.minutes
    )
  end

  def self.update_event_as_completed(event, consultation_request)
    completed_title = "[COMPLETED] Consultation: #{consultation_request.student_name}"
    completed_description = build_event_description(consultation_request, completed: true)
    
    event.update!(
      title: completed_title,
      description: completed_description
    )
  end

  def self.cancel_calendar_event(event, consultation_request)
    cancelled_title = "[CANCELLED] Consultation: #{consultation_request.student_name}"
    cancel_reason = case consultation_request.status
                   when 'declined'
                     consultation_request.faculty_comment || 'Declined by faculty'
                   when 'cancelled'
                     'Cancelled by student'
                   else
                     'Consultation cancelled'
                   end

    event.update!(
      title: cancelled_title,
      description: build_event_description(consultation_request, cancelled: true, cancel_reason: cancel_reason),
      workflow_state: 'deleted'
    )
  end

  def self.build_event_description(consultation_request, completed: false, cancelled: false, cancel_reason: nil)
    description = []
    
    if completed
      description << "✅ COMPLETED CONSULTATION"
    elsif cancelled
      description << "❌ CANCELLED CONSULTATION"
      description << "Reason: #{cancel_reason}" if cancel_reason
    else
      description << "📅 SCHEDULED CONSULTATION"
    end
    
    description << ""
    description << "Student: #{consultation_request.student_name} (#{consultation_request.student_id})"
    description << "Concern Type: #{consultation_request.concern_type_display}"
    description << "Duration: 30 minutes"
    description << ""
    description << "Student's Description:"
    description << consultation_request.description
    
    if consultation_request.faculty_comment.present?
      description << ""
      description << "Faculty Notes:"
      description << consultation_request.faculty_comment
    end
    
    description << ""
    description << "---"
    description << "Consultation Request ID: #{consultation_request.id}"
    description << "consultation_request_id:#{consultation_request.id}"
    
    description.join("\n")
  end

  def self.get_faculty_calendar_events(faculty_user, start_date, end_date)
    CalendarEvent.active
                 .where(context: faculty_user)
                 .where(start_at: start_date.beginning_of_day..end_date.end_of_day)
                 .order(:start_at)
  end

  def self.get_consultation_events_for_faculty(faculty_user, start_date, end_date)
    CalendarEvent.active
                 .where(context: faculty_user)
                 .where(start_at: start_date.beginning_of_day..end_date.end_of_day)
                 .where("title LIKE ? OR description LIKE ?", "%Consultation:%", "%consultation_request_id:%")
                 .order(:start_at)
  end

  # Method to sync all approved consultations to calendar
  def self.sync_all_consultations_to_calendar
    ConsultationRequest.approved.includes(:faculty, :student).find_each do |consultation|
      begin
        create_calendar_event_for_consultation(consultation)
      rescue => e
        Rails.logger.error "Failed to sync consultation #{consultation.id} to calendar: #{e.message}"
      end
    end
  end

  # Method to clean up orphaned calendar events
  def self.cleanup_orphaned_consultation_events
    # Find calendar events that reference consultation requests that no longer exist or are not approved
    consultation_events = CalendarEvent.active
                                      .where("title LIKE ? OR description LIKE ?", "%Consultation:%", "%consultation_request_id:%")

    consultation_events.find_each do |event|
      # Extract consultation request ID from comments or description
      consultation_id = extract_consultation_id_from_event(event)
      next unless consultation_id

      consultation = ConsultationRequest.find_by(id: consultation_id)
      
      # If consultation doesn't exist or is not approved, mark event as deleted
      if consultation.nil? || !consultation.approved?
        event.update!(workflow_state: 'deleted')
        Rails.logger.info "Cleaned up orphaned calendar event #{event.id} for consultation #{consultation_id}"
      end
    end
  end

  def self.extract_consultation_id_from_event(event)
    # Try to extract from comments first
    if event.comments&.include?('consultation_request_id:')
      return event.comments.match(/consultation_request_id:(\d+)/)&.[](1)&.to_i
    end

    # Try to extract from description
    if event.description&.include?('consultation_request_id:')
      return event.description.match(/consultation_request_id:(\d+)/)&.[](1)&.to_i
    end

    nil
  end
end
