<%
# Copyright (C) 2011 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
%>

<% css_bundle :reports %>
<% js_bundle :teacher_activity_report %>
<% add_crumb(t('crumbs.interaction_report', "Student Interactions Report")) %>

<% provide :page_title, t('title', 'Teacher Activity Report') %>

<div class="teacher-activity-container">
  <div class="modern-header">
    <h1><%= t('headings.teacher_activity', "Teacher Activity Report for %{teacher}", :teacher => @teacher.name) %></h1>
    <p class="subtitle">Student engagement and performance overview</p>
  </div>

  <% @courses.each do |course, students| %>
    <div class="course-section">
      <div class="course-header">
        <h2 class="course-title"><%= course.name %></h2>
      </div>
      
      <% if students.blank? %>
        <div class="alert-section">
          <h3><%= t 'no_students', 'There are no students to report on.' %></h3>
        </div>
      <% else %>
        
        <!-- Main Tab Navigation -->
        <div class="main-tab-navigation">
          <button class="main-tab-btn active" onclick="showMainTab('<%= course.id %>', 'student-overview')">
            👥 Student Overview
          </button>
          <button class="main-tab-btn" onclick="showMainTab('<%= course.id %>', 'student-analytics')">
            📊 Student Analytics
          </button>
          <button class="main-tab-btn" onclick="showMainTab('<%= course.id %>', 'grade-distribution')">
            📈 Grade Distribution
          </button>
          <button class="main-tab-btn" onclick="showMainTab('<%= course.id %>', 'trend-analysis')">
            📈 Trend Analysis
          </button>
          <button class="main-tab-btn" onclick="showMainTab('<%= course.id %>', 'performance-report')">
            📋 Performance Report
          </button>
        </div>

        <!-- Student Overview Tab Content -->
        <div class="main-tab-content active" id="student-overview-<%= course.id %>">
          <% # Collect at-risk students for alert section %>
          <% at_risk_students = students.select do |student|
               last_access = student[:access_report] && student[:access_report][:last_access]
               last_access.nil? || last_access < 7.days.ago
             end %>

          <% if at_risk_students.any? %>
            <div class="alert-section">
              <h3>⚠️ Students Requiring Attention</h3>
              <div class="alert-list">
                <% at_risk_students.each do |student| %>
                  <div class="alert-item">
                    <%= student[:enrollment].user.name %> - No activity >7d
                  </div>
                <% end %>
              </div>
            </div>
          <% end %>

          <div class="students-grid">
            <% students.each do |student| %>
              <% 
                flags = []
                
                # Get course assignments for submission checking
                all_assignments = course.assignments.where(workflow_state: 'published')
                gradeable_assignments = all_assignments.where.not(submission_types: 'not_graded')
                
                # Categorize submissions into different types
                student_submitted = []           # Student actually submitted work
                manually_graded = []            # Teacher entered grade without submission
                ungraded_submissions = []       # Student submitted but no grade yet
                no_work_no_grade = []          # No submission, no grade
                
                gradeable_assignments.each do |assignment|
                  submission = assignment.submissions.find_by(user_id: student[:enrollment].user_id)
                  
                  if submission.present?
                    if submission.submitted_at.present? && submission.score.present?
                      # Student submitted AND has been graded
                      student_submitted << submission
                    elsif submission.submitted_at.present? && submission.score.nil?
                      # Student submitted but NOT graded yet
                      ungraded_submissions << submission
                    elsif submission.submitted_at.nil? && submission.score.present?
                      # Manual grade entry (no student submission)
                      manually_graded << submission
                    else
                      # No submission, no grade
                      no_work_no_grade << assignment
                    end
                  else
                    # No submission record at all
                    no_work_no_grade << assignment
                  end
                end
                
                # Calculate totals
                total_graded = student_submitted.count + manually_graded.count
                total_student_work = student_submitted.count + ungraded_submissions.count
              %>
              
              <%# Calculate activity-based flags first %>
              <% last_access = student[:access_report] && student[:access_report][:last_access] %>
              <% if last_access.nil? || last_access < 7.days.ago %>
                <% flags << { text: "No activity >7d", type: "critical" } %>
              <% end %>
              
              <% avg_views = students.map { |s| s[:access_report]&.[](:total_views).to_i }.sum / students.size.to_f %>
              <% if student[:access_report] && student[:access_report][:total_views].to_i < (avg_views * 0.5) %>
                <% flags << { text: "Low interaction", type: "warning" } %>
              <% end %>
              
              <% ar = student[:access_report] %>
              <% if ar && ar[:views_last_7_days] && ar[:views_prev_7_days] %>
                <% prev = ar[:views_prev_7_days].to_i; curr = ar[:views_last_7_days].to_i %>
                <% if prev > 0 && curr < (prev * 0.3) %>
                  <% flags << { text: "Sudden drop", type: "warning" } %>
                <% end %>
              <% end %>

              <%# SMART FLAGS based on submission/grading scenarios %>
              
              <%# Scenario 1: Student has scores but didn't submit - likely manual grades %>
              <% if manually_graded.any? && student_submitted.empty? %>
                <% flags << { text: "#{manually_graded.count} manual grades (no submissions)", type: "info" } %>
              <% end %>
              
              <%# Scenario 2: Student submitted work but nothing graded %>
              <% if ungraded_submissions.count >= 3 %>
                <% flags << { text: "#{ungraded_submissions.count} submissions need grading", type: "info" } %>
              <% end %>
              
              <%# Scenario 3: No student work AND no manual grades %>
              <% if total_student_work == 0 && manually_graded.empty? %>
                <% flags << { text: "No work submitted", type: "critical" } %>
              <% end %>
              
              <%# Scenario 4: Mix of manual and submitted work %>
              <% if manually_graded.any? && student_submitted.any? %>
                <% flags << { text: "Mixed: #{student_submitted.count} submitted, #{manually_graded.count} manual", type: "info" } %>
              <% end %>
              
              <%# Scenario 5: Low actual submission rate (but maybe high manual grades) %>
              <% if gradeable_assignments.any? %>
                <% student_submission_rate = (total_student_work.to_f / gradeable_assignments.count) * 100 %>
                <% if student_submission_rate < 30 && manually_graded.count < 3 %>
                  <% flags << { text: "Low activity (#{student_submission_rate.round}% submission rate)", type: "warning" } %>
                <% end %>
              <% end %>
              
              <%# Scenario 6: Check if scores exist but submission tracking is off %>
              <% current_score = student[:enrollment].computed_current_score %>
              <% if current_score.present? && current_score > 0 %>
                <% if total_graded == 0 %>
                  <%# Student has computed score but our tracking shows no grades - data inconsistency %>
                  <% flags << { text: "Has course score but no tracked grades", type: "warning" } %>
                <% elsif current_score > 70 && total_student_work == 0 %>
                  <%# Good score with no student submissions - likely all manual grades %>
                  <% flags << { text: "Good grades via manual entry", type: "good" } %>
                <% end %>
              <% end %>
              
              <%# Missing recent assignments %>
              <% 
                recent_assignments = gradeable_assignments.where('due_at > ? OR (due_at IS NULL AND created_at > ?)', 2.weeks.ago, 2.weeks.ago)
                missing_recent = recent_assignments.select do |assignment|
                  submission = assignment.submissions.find_by(user_id: student[:enrollment].user_id)
                  submission.nil? || submission.submitted_at.nil?
                end
              %>
              <% if missing_recent.count >= 2 %>
                <% flags << { text: "Missing #{missing_recent.count} recent assignments", type: "warning" } %>
              <% end %>
              
              <%# If no concerning flags, mark as active %>
              <% concerning_flags = flags.select { |f| ['critical', 'warning'].include?(f[:type]) } %>
              <% if concerning_flags.empty? %>
                <% flags << { text: "Active", type: "good" } %>
              <% end %>

              <div class="student-card">
                <div class="student-header">
                  <%= link_to(student[:enrollment].user.name, course_user_url(course, student[:enrollment].user_id), class: "student-name") %>
                  <div class="student-actions">
                    <% if course.user_has_been_instructor?(@current_user) %>
                      <%= link_to message_user_path(student[:enrollment].user, course), class: "message_student_link", title: t('message_student', 'Message this student') do %>
                        📧
                      <% end %>
                    <% end %>
                    <a href="<%= context_url(course, :context_user_usage_url, student[:enrollment].user) %>" 
                      class="action-btn" title="View Full Report">
                      📊
                    </a>
                  </div>
                </div>
                
                <div class="student-metrics">
                  <div class="metric">
                    <div class="metric-value"><%= n(student[:enrollment].computed_current_score, percentage: true, precision: 1) %></div>
                    <div class="metric-label">Current Score</div>
                    <div class="progress-bar">
                      <div class="progress-fill" style="width: <%= student[:enrollment].computed_current_score || 0 %>%"></div>
                    </div>
                  </div>
                  <div class="metric">
                    <div class="metric-value"><%= n(student[:enrollment].computed_final_score, percentage: true, precision: 1) %></div>
                    <div class="metric-label">Final Score</div>
                    <div class="progress-bar">
                      <div class="progress-fill" style="width: <%= student[:enrollment].computed_final_score || 0 %>%"></div>
                    </div>
                  </div>
                </div>

                <%# ENHANCED submission summary showing the breakdown %>
                <div class="submission-summary">
                  <div class="submission-breakdown">
                    <% if gradeable_assignments.any? %>
                      <div class="breakdown-section">
                        <div class="breakdown-item">
                          <span class="breakdown-label">Student Work:</span>
                          <span class="breakdown-value">
                            <strong><%= student_submitted.count %></strong> submitted & graded
                            <% if ungraded_submissions.any? %>
                              , <strong><%= ungraded_submissions.count %></strong> pending
                            <% end %>
                          </span>
                        </div>
                        
                        <% if manually_graded.any? %>
                          <div class="breakdown-item manual-grades">
                            <span class="breakdown-label">Manual Grades:</span>
                            <span class="breakdown-value">
                              <strong><%= manually_graded.count %></strong> assignments
                              <span class="manual-note">(teacher-entered)</span>
                            </span>
                          </div>
                        <% end %>
                        
                        <div class="breakdown-item totals">
                          <span class="breakdown-label">Total Progress:</span>
                          <span class="breakdown-value">
                            <strong><%= total_graded %></strong> graded of 
                            <strong><%= gradeable_assignments.count %></strong> assignments
                            (<strong><%= ((total_graded.to_f / gradeable_assignments.count) * 100).round %>%</strong>)
                          </span>
                        </div>
                      </div>
                    <% else %>
                      <div class="breakdown-item">
                        <span class="breakdown-value">No gradeable assignments yet</span>
                      </div>
                    <% end %>
                  </div>
                </div>

                <% if student[:access_report] %>
                  <div class="access-summary">
                    <div class="access-stats">
                      <div class="stat">
                        <span class="label">Views</span>
                        <span class="value"><%= student[:access_report][:total_views] %></span>
                      </div>
                      <div class="stat">
                        <span class="label">Participations</span>
                        <span class="value"><%= student[:access_report][:total_participations] %></span>
                      </div>
                      <div class="stat">
                        <span class="label">Items</span>
                        <span class="value"><%= student[:access_report][:unique_items_accessed] %></span>
                      </div>
                    </div>
                    <div class="last-interaction">
                      <% if student[:last_interaction] %>
                        Last interaction: <%= t 'last_time', { :zero => "less than 1 day ago", :one => "1 day ago", :other => "%{count} days ago" }, :count => (((Time.now - student[:last_interaction])/60)/1440).abs.to_i %>
                      <% else %>
                        Last interaction: <%= t 'last_time_never', 'never' %>
                      <% end %>
                      <% if student[:access_report][:last_access] %>
                        • Last access: <%= time_ago_in_words(student[:access_report][:last_access]) %> ago
                      <% end %>
                    </div>
                    <a href="<%= context_url(course, :context_user_usage_url, student[:enrollment].user) %>" 
                      class="access-link">View Full Report</a>
                  </div>
                <% else %>
                  <div class="access-summary">
                    <span style="color: #666; font-style: italic;">No activity data available</span>
                  </div>
                <% end %>

                <% if student[:ungraded].any? %>
                  <div class="ungraded-assignments">
                    <div class="ungraded-title">📝 Ungraded Assignments (<%= student[:ungraded].size %>)</div>
                    <% student[:ungraded].each do |submission| %>
                      <%= link_to(submission.assignment.title, speed_grader_course_gradebook_path(course, assignment_id: submission.assignment.id, student_id: student[:enrollment].user_id), class: "assignment-link") %>
                      <% if submission.submitted_at %>
                        <div class="assignment-date">
                          <%= t 'submitted_time', { :zero => "submitted less than 1 day ago", :one => "submitted 1 day ago", :other => "submitted %{count} days ago" }, :count => (((Time.now - submission.submitted_at)/60)/1440).abs.to_i %>
                        </div>
                      <% end %>
                    <% end %>
                  </div>
                <% end %>

                <%# Enhanced flags display %>
                <div class="student-flags">
                  <% flags.each do |flag| %>
                    <span class="flag <%= flag[:type] %>">
                      <% case flag[:type] %>
                      <% when 'critical' %>
                        🚨 <%= flag[:text] %>
                      <% when 'warning' %>
                        ⚠️ <%= flag[:text] %>
                      <% when 'info' %>
                        ℹ️ <%= flag[:text] %>
                      <% when 'good' %>
                        ✅ <%= flag[:text] %>
                      <% else %>
                        <%= flag[:text] %>
                      <% end %>
                    </span>
                  <% end %>
                </div>
              </div>
            <% end %>
          </div>
        </div>

        <!-- Student Analytics Tab Content -->
        <div class="main-tab-content" id="student-analytics-<%= course.id %>">
        
          <% if students.present? %>
            <div class="charts-section">
              <div class="charts-header">
                <h3>📊 Student Activity Analytics</h3>
                <p>Comprehensive overview of student engagement patterns and learning behaviors</p>
              </div>
              
              <!-- Navigation Tabs -->
              <div class="chart-navigation">
                <button class="chart-tab-btn active" onclick="showChart('<%= course.id %>', 'views')">
                  📖 Page Views
                </button>
                <button class="chart-tab-btn" onclick="showChart('<%= course.id %>', 'participations')">
                  🗣️ Participations
                </button>
                <button class="chart-tab-btn" onclick="showChart('<%= course.id %>', 'items')">
                  📚 Content Items
                </button>
              </div>

              <!-- Views Tab Content -->
              <div class="chart-tab-content active" id="views-content-<%= course.id %>">
                <div class="metric-description">
                  <h4>📖 What are Page Views?</h4>
                  <p>Page views represent each time a student accesses course content including assignment pages, discussion posts, module items, announcements, and other course materials. Higher view counts typically indicate more engaged students who actively browse and revisit course content.</p>
                </div>
                
                <div class="chart-container">
                  <canvas 
                    class="course-activity-chart"
                    id="course-views-chart-<%= course.id %>"
                    width="600" height="250"
                    data-labels='<%= students.map { |s| s[:enrollment].user.name }.to_json %>'
                    data-data='<%= students.map { |s| s[:access_report] ? s[:access_report][:total_views] : 0 }.to_json %>'
                    data-label="Views"
                    data-color="#2d5b3f"
                  ></canvas>
                </div>
                
                <% 
                  view_data = students.map { |s| s[:access_report] ? s[:access_report][:total_views].to_i : 0 }
                  avg_views = view_data.sum / students.size.to_f
                  max_views = view_data.max
                  low_activity_count = view_data.count { |v| v < 50 }
                %>
                
                <div class="metric-stats">
                  <div class="metric-stat">
                    <span class="stat-value"><%= avg_views.round %></span>
                    <span class="stat-label">Average Views</span>
                  </div>
                  <div class="metric-stat">
                    <span class="stat-value"><%= max_views %></span>
                    <span class="stat-label">Highest Views</span>
                  </div>
                  <div class="metric-stat">
                    <span class="stat-value"><%= low_activity_count %></span>
                    <span class="stat-label">Students < 50 Views</span>
                  </div>
                </div>
              </div>
              
              <!-- Participations Tab Content -->
              <div class="chart-tab-content" id="participations-content-<%= course.id %>">
                <div class="metric-description">
                  <h4>🗣️ What are Participations?</h4>
                  <p>Participations count active student contributions including discussion posts, replies, quiz submissions, assignment uploads, and other interactive activities. This metric measures actual engagement rather than passive consumption of content.</p>
                </div>
                
                <div class="chart-container">
                  <canvas 
                    class="course-activity-chart"
                    id="course-participations-chart-<%= course.id %>"
                    width="600" height="250"
                    data-labels='<%= students.map { |s| s[:enrollment].user.name }.to_json %>'
                    data-data='<%= students.map { |s| s[:access_report] ? s[:access_report][:total_participations] : 0 }.to_json %>'
                    data-label="Participations"
                    data-color="#f4a534"
                  ></canvas>
                </div>
                
                <% 
                  participation_data = students.map { |s| s[:access_report] ? s[:access_report][:total_participations].to_i : 0 }
                  avg_participations = participation_data.sum / students.size.to_f
                  max_participations = participation_data.max
                  no_participation_count = participation_data.count { |p| p == 0 }
                %>
                
                <div class="metric-stats">
                  <div class="metric-stat">
                    <span class="stat-value"><%= avg_participations.round(1) %></span>
                    <span class="stat-label">Average Participations</span>
                  </div>
                  <div class="metric-stat">
                    <span class="stat-value"><%= max_participations %></span>
                    <span class="stat-label">Most Active Student</span>
                  </div>
                  <div class="metric-stat">
                    <span class="stat-value"><%= no_participation_count %></span>
                    <span class="stat-label">Students with 0 Participations</span>
                  </div>
                </div>
              </div>
              
              <!-- Items Tab Content -->
              <div class="chart-tab-content" id="items-content-<%= course.id %>">
                <div class="metric-description">
                  <h4>📚 What are Content Items?</h4>
                  <p>Content items represent unique course materials accessed by students including assignments, files, videos, external links, pages, and quizzes. This metric shows content breadth - how much of the available course material each student has explored.</p>
                </div>
                
                <div class="chart-container">
                  <canvas 
                    class="course-activity-chart"
                    id="course-items-chart-<%= course.id %>"
                    width="600" height="250"
                    data-labels='<%= students.map { |s| s[:enrollment].user.name }.to_json %>'
                    data-data='<%= students.map { |s| s[:access_report] ? s[:access_report][:unique_items_accessed] : 0 }.to_json %>'
                    data-label="Items"
                    data-color="#3d6b4f"
                  ></canvas>
                </div>
                
                <% 
                  items_data = students.map { |s| s[:access_report] ? s[:access_report][:unique_items_accessed].to_i : 0 }
                  avg_items = items_data.sum / students.size.to_f
                  max_items = items_data.max
                  total_available_items = max_items > 0 ? [max_items + 5, 20].min : 18
                  completion_rate = max_items > 0 ? ((avg_items / total_available_items) * 100).round : 0
                %>
                
                <div class="metric-stats">
                  <div class="metric-stat">
                    <span class="stat-value"><%= avg_items.round(1) %></span>
                    <span class="stat-label">Average Items</span>
                  </div>
                  <div class="metric-stat">
                    <span class="stat-value"><%= total_available_items %></span>
                    <span class="stat-label">Estimated Available</span>
                  </div>
                  <div class="metric-stat">
                    <span class="stat-value"><%= completion_rate %>%</span>
                    <span class="stat-label">Average Completion</span>
                  </div>
                </div>
              </div>
            </div>
          <% else %>
            <div class="empty-state">
              <h3>No student data available</h3>
              <p>Analytics will appear here once students begin engaging with the course.</p>
            </div>
          <% end %>
        </div>

        <!-- Grade Distribution Analysis Tab Content -->
        <div class="main-tab-content" id="grade-distribution-<%= course.id %>">
          
          <div class="charts-section">
            <div class="charts-header">
              <h3>📊 Grade Distribution Analysis</h3>
              <p>Assignment and quiz performance overview with detailed statistics</p>
            </div>
            
            <!-- Grade Distribution Content -->
            <div class="grade-distribution-container">
              <% 
                # Get ONLY published assignments and quizzes for this course
                assignments = course.assignments.preload(submissions: :user).where(workflow_state: 'published')
                quizzes = course.quizzes.preload(quiz_submissions: :user).where(workflow_state: 'published') if course.respond_to?(:quizzes)
                
                assignment_stats = []
                
                # Process regular assignments (only published ones)
                assignments.each do |assignment|
                  submissions = assignment.submissions.joins(:user).where(users: { id: students.map { |s| s[:enrollment].user_id } })
                  submitted_submissions = submissions.where.not(submitted_at: nil)
                  graded_submissions = submitted_submissions.where.not(score: nil)
                  
                  # Get all scores and student details
                  all_scores = []
                  student_grades = []
                  students.each do |student|
                    submission = submissions.find { |s| s.user_id == student[:enrollment].user_id }
                    
                    # Fix: Handle different student data structures
                    student_name = if student[:user]&.name
                      student[:user].name
                    elsif student[:enrollment]&.user&.name
                      student[:enrollment].user.name
                    elsif student.respond_to?(:name)
                      student.name
                    elsif student.is_a?(Hash) && student[:name]
                      student[:name]
                    else
                      "Unknown Student"
                    end
                    
                    if submission&.score.present?
                      all_scores << submission.score
                      student_grades << {
                        name: student_name,
                        score: submission.score,
                        status: 'graded',
                        submitted_at: submission.submitted_at
                      }
                    elsif assignment.submission_types != 'not_graded'
                      # Count missing submissions as 0 for published graded assignments
                      if submission.nil? || submission.submitted_at.nil?
                        all_scores << 0
                        student_grades << {
                          name: student_name,
                          score: 0,
                          status: 'missing',
                          submitted_at: nil
                        }
                      else
                        # Submitted but not graded yet
                        student_grades << {
                          name: student_name,
                          score: nil,
                          status: 'pending',
                          submitted_at: submission.submitted_at
                        }
                      end
                    else
                      # Non-gradeable assignment
                      status = submission&.submitted_at ? 'submitted' : 'not_submitted'
                      student_grades << {
                        name: student_name,
                        score: nil,
                        status: status,
                        submitted_at: submission&.submitted_at
                      }
                    end
                  end
                  
                  assignment_stats << {
                    item: assignment,
                    type: assignment.submission_types.include?('online_quiz') ? 'Quiz' : 'Assignment',
                    total_students: students.count,
                    submitted_count: submitted_submissions.count,
                    graded_count: graded_submissions.count,
                    missing_count: students.count - submitted_submissions.count,
                    average_score: all_scores.any? ? (all_scores.sum.to_f / all_scores.count) : nil,
                    highest_score: all_scores.any? ? all_scores.max : nil,
                    lowest_score: all_scores.any? ? all_scores.min : nil,
                    scores: all_scores,
                    student_grades: student_grades.sort_by { |sg| sg[:name] },
                    max_points: assignment.points_possible || 100,
                    gradeable: assignment.submission_types != 'not_graded',
                    due_date: assignment.due_at
                  }
                end
                
                # Process quizzes separately if they exist (only published ones)
                if defined?(quizzes) && quizzes.any?
                  quizzes.each do |quiz|
                    quiz_submissions = quiz.quiz_submissions.joins(:user).where(users: { id: students.map { |s| s[:enrollment].user_id } })
                    completed_submissions = quiz_submissions.where(workflow_state: 'complete')
                    
                    # Get all scores and student details
                    all_scores = []
                    student_grades = []
                    students.each do |student|
                      submission = quiz_submissions.find { |s| s.user_id == student[:enrollment].user_id }
                      
                      # Fix: Handle different student data structures
                      student_name = if student[:user]&.name
                        student[:user].name
                      elsif student[:enrollment]&.user&.name
                        student[:enrollment].user.name
                      elsif student.respond_to?(:name)
                        student.name
                      elsif student.is_a?(Hash) && student[:name]
                        student[:name]
                      else
                        "Unknown Student"
                      end
                      
                      if submission&.score.present?
                        all_scores << submission.score
                        student_grades << {
                          name: student_name,
                          score: submission.score,
                          status: 'graded',
                          submitted_at: submission.finished_at
                        }
                      else
                        # Count missing/incomplete quiz submissions as 0 for published quizzes
                        if submission.nil? || submission.workflow_state != 'complete'
                          all_scores << 0
                          status = submission&.workflow_state == 'started' ? 'in_progress' : 'missing'
                          student_grades << {
                            name: student_name,
                            score: 0,
                            status: status,
                            submitted_at: submission&.finished_at
                          }
                        end
                      end
                    end
                    
                    assignment_stats << {
                      item: quiz,
                      type: 'Quiz',
                      total_students: students.count,
                      submitted_count: completed_submissions.count,
                      graded_count: completed_submissions.count,
                      missing_count: students.count - completed_submissions.count,
                      average_score: all_scores.any? ? (all_scores.sum.to_f / all_scores.count) : nil,
                      highest_score: all_scores.any? ? all_scores.max : nil,
                      lowest_score: all_scores.any? ? all_scores.min : nil,
                      scores: all_scores,
                      student_grades: student_grades.sort_by { |sg| sg[:name] },
                      max_points: quiz.points_possible || 100,
                      gradeable: true,
                      due_date: quiz.due_at
                    }
                  end
                end
                
                # Sort by due date (newest first) and then by creation date
                assignment_stats.sort_by! { |stat| [stat[:due_date] || Time.current, stat[:item].created_at] }.reverse!
              %>
              
              <% if assignment_stats.any? %>
                <div class="assignments-grid">
                  <% assignment_stats.each_with_index do |stats, index| %>
                    <div class="assignment-card <%= 'no-grades' unless stats[:gradeable] %>">
                      <div class="assignment-header">
                        <h4 class="assignment-title">
                          <%= stats[:item].title %>
                          <span class="assignment-type <%= stats[:type].downcase %>">
                            <%= stats[:type] == 'Quiz' ? '📝' : '📋' %> <%= stats[:type] %>
                          </span>
                          <% unless stats[:gradeable] %>
                            <span class="assignment-status not-graded">ℹ️ Not Graded</span>
                          <% end %>
                        </h4>
                        <div class="assignment-meta">
                          <div class="assignment-points"><%= stats[:max_points] %> points</div>
                          <% if stats[:due_date] %>
                            <div class="assignment-due">Due: <%= stats[:due_date].strftime("%m/%d/%Y") %></div>
                          <% else %>
                            <div class="assignment-due">No due date</div>
                          <% end %>
                        </div>
                      </div>
                      
                      <% if stats[:gradeable] %>
                        <!-- 1. Grade Chart -->
                        <% if stats[:scores].any? %>
                          <div class="grade-chart-container">
                            <canvas 
                              class="grade-distribution-chart"
                              id="grade-chart-<%= course.id %>-<%= index %>"
                              width="400" height="200"
                              data-scores='<%= stats[:scores].to_json %>'
                              data-max-points="<%= stats[:max_points] %>"
                              data-assignment="<%= stats[:item].title %>"
                            ></canvas>
                          </div>
                          
                          <!-- 2. Grade Breakdown -->
                          <div class="grade-breakdown">
                            <% 
                              grade_ranges = {
                                'A (90-100%)' => stats[:scores].count { |s| (s / stats[:max_points]) >= 0.9 },
                                'B (80-89%)' => stats[:scores].count { |s| (s / stats[:max_points]) >= 0.8 && (s / stats[:max_points]) < 0.9 },
                                'C (70-79%)' => stats[:scores].count { |s| (s / stats[:max_points]) >= 0.7 && (s / stats[:max_points]) < 0.8 },
                                'D (60-69%)' => stats[:scores].count { |s| (s / stats[:max_points]) >= 0.6 && (s / stats[:max_points]) < 0.7 },
                                'F (Below 60%)' => stats[:scores].count { |s| (s / stats[:max_points]) < 0.6 }
                              }
                            %>
                            
                            <div class="breakdown-title">Grade Distribution</div>
                            <div class="breakdown-bars">
                              <% grade_ranges.each do |grade, count| %>
                                <div class="breakdown-item">
                                  <span class="breakdown-label"><%= grade %></span>
                                  <div class="breakdown-bar">
                                    <% bar_width = stats[:scores].count > 0 ? (count.to_f / stats[:scores].count * 100).round(1) : 0 %>
                                    <div class="breakdown-fill" style="width: <%= bar_width % 1 == 0 ? bar_width.round(0) : bar_width %>%"></div>
                                  </div>
                                  <span class="breakdown-count"><%= count %></span>
                                </div>
                              <% end %>
                            </div>
                          </div>
                        <% else %>
                          <div class="no-submissions">
                            <p>📝 No submissions graded yet</p>
                          </div>
                        <% end %>
                        
                        <!-- 3. Grade Stats -->
                        <div class="grade-stats-grid">
                          <div class="grade-stat">
                            <span class="stat-value">
                              <%= stats[:average_score] ? (stats[:average_score].round(1) % 1 == 0 ? stats[:average_score].round(0) : stats[:average_score].round(1)) : 'N/A' %>
                            </span>
                            <span class="stat-label">Average Score</span>
                            <% if stats[:average_score] && stats[:max_points] && stats[:max_points] > 0 %>
                              <div class="stat-percentage">
                                <% percentage = ((stats[:average_score] / stats[:max_points]) * 100).round(1) %>
                                <%= percentage % 1 == 0 ? percentage.round(0) : percentage %>%
                              </div>
                            <% end %>
                          </div>
                          
                          <div class="grade-stat">
                            <span class="stat-value">
                              <%= stats[:highest_score] ? (stats[:highest_score].round(1) % 1 == 0 ? stats[:highest_score].round(0) : stats[:highest_score].round(1)) : 'N/A' %>
                            </span>
                            <span class="stat-label">Highest Score</span>
                            <% if stats[:highest_score] && stats[:max_points] && stats[:max_points] > 0 %>
                              <div class="stat-percentage">
                                <% percentage = ((stats[:highest_score] / stats[:max_points]) * 100).round(1) %>
                                <%= percentage % 1 == 0 ? percentage.round(0) : percentage %>%
                              </div>
                            <% end %>
                          </div>
                          
                          <div class="grade-stat">
                            <span class="stat-value">
                              <%= stats[:lowest_score] ? (stats[:lowest_score].round(1) % 1 == 0 ? stats[:lowest_score].round(0) : stats[:lowest_score].round(1)) : 'N/A' %>
                            </span>
                            <span class="stat-label">Lowest Score</span>
                            <% if stats[:lowest_score] && stats[:max_points] && stats[:max_points] > 0 %>
                              <div class="stat-percentage">
                                <% percentage = ((stats[:lowest_score] / stats[:max_points]) * 100).round(1) %>
                                <%= percentage % 1 == 0 ? percentage.round(0) : percentage %>%
                              </div>
                            <% end %>
                          </div>
                          
                          <div class="grade-stat">
                            <span class="stat-value"><%= stats[:submitted_count] %>/<%= stats[:total_students] %></span>
                            <span class="stat-label">Submitted</span>
                            <div class="stat-percentage">
                              <% percentage = ((stats[:submitted_count].to_f / stats[:total_students]) * 100).round(1) %>
                              <%= percentage % 1 == 0 ? percentage.round(0) : percentage %>%
                            </div>
                          </div>
                        </div>
                        
                        <% if stats[:missing_count] > 0 %>
                          <div class="missing-submissions">
                            <span class="missing-icon">⚠️</span>
                            <span class="missing-text"><%= stats[:missing_count] %> students haven't submitted</span>
                          </div>
                        <% end %>
                        
                        <!-- Individual Student Grades -->
                        <div class="student-grades-section">
                          <div class="student-grades-header">
                            <h5>👥 Individual Student Grades</h5>
                            <button class="toggle-grades-btn" onclick="toggleStudentGrades('<%= course.id %>-<%= index %>')">
                              <span class="toggle-text">Show Details</span>
                              <span class="toggle-icon">▼</span>
                            </button>
                          </div>
                          
                          <div class="student-grades-list" id="student-grades-<%= course.id %>-<%= index %>" style="display: none;">
                            <div class="grades-table">
                              <div class="grades-header">
                                <div class="grade-col-student">Student Name</div>
                                <div class="grade-col-score">Score</div>
                                <div class="grade-col-percentage">Percentage</div>
                                <div class="grade-col-status">Status</div>
                                <div class="grade-col-submitted">Submitted</div>
                              </div>
                              
                              <% stats[:student_grades].each do |student_grade| %>
                                <div class="grade-row <%= student_grade[:status] %>">
                                  <div class="grade-col-student">
                                    <span class="student-name"><%= student_grade[:name] %></span>
                                  </div>
                                  <div class="grade-col-score">
                                    <% if student_grade[:score].present? %>
                                      <% 
                                        # Format score to remove unnecessary decimals
                                        formatted_score = student_grade[:score] % 1 == 0 ? student_grade[:score].to_i : student_grade[:score]
                                        formatted_max = stats[:max_points] % 1 == 0 ? stats[:max_points].to_i : stats[:max_points]
                                      %>
                                      <%= formatted_score %>/<%= formatted_max %>
                                    <% else %>
                                      <span class="no-score">—</span>
                                    <% end %>
                                  </div>
                                  <div class="grade-col-percentage">
                                    <% if student_grade[:score].present? && stats[:max_points] && stats[:max_points] > 0 %>
                                      <span class="percentage">
                                        <% percentage = ((student_grade[:score] / stats[:max_points]) * 100).round(1) %>
                                        <%= percentage % 1 == 0 ? percentage.round(0) : percentage %>%
                                      </span>
                                    <% else %>
                                      <span class="no-percentage">—</span>
                                    <% end %>
                                  </div>
                                  <div class="grade-col-status">
                                    <span class="status-badge <%= student_grade[:status] %>">
                                      <% case student_grade[:status] %>
                                      <% when 'graded' %>
                                        Graded
                                      <% when 'pending' %>
                                        Pending
                                      <% when 'missing' %>
                                        Missing
                                      <% when 'in_progress' %>
                                        In Progress
                                      <% when 'submitted' %>
                                        Submitted
                                      <% when 'not_submitted' %>
                                        Not Submitted
                                      <% end %>
                                    </span>
                                  </div>
                                  <div class="grade-col-submitted">
                                    <% if student_grade[:submitted_at] %>
                                      <%= student_grade[:submitted_at].strftime("%m/%d/%Y %I:%M %p") %>
                                    <% else %>
                                      <span class="no-submission">—</span>
                                    <% end %>
                                  </div>
                                </div>
                              <% end %>
                            </div>
                          </div>
                        </div>
                        
                        <% if stats[:average_score] && stats[:average_score] < (stats[:max_points] * 0.7) %>
                          <div class="assignment-alert">
                            ⚠️ Below average performance - consider reviewing this material
                          </div>
                        <% elsif stats[:submitted_count] < (stats[:total_students] * 0.8) %>
                          <div class="assignment-alert warning">
                            📝 Low submission rate - follow up with missing students
                          </div>
                        <% end %>
                      <% else %>
                        <!-- For non-gradeable assignments -->
                        <div class="assignment-status-info">
                          <div class="status-message">
                            ℹ️ This assignment is not set up for grading
                          </div>
                          <div class="basic-stats">
                            <div class="basic-stat">
                              <span class="stat-value"><%= stats[:submitted_count] %></span>
                              <span class="stat-label">Submissions</span>
                            </div>
                          </div>
                        </div>
                      <% end %>
                    </div>
                  <% end %>
                </div>
              <% else %>
                <div class="no-grades-message">
                  <h4>📊 No Published Assignments or Quizzes Found</h4>
                  <p>There are no published assignments or quizzes for this course yet.</p>
                  <div class="create-assignment-prompt">
                    <p>💡 <strong>Get started:</strong> Create and publish assignments and quizzes to see grade distribution analysis here.</p>
                  </div>
                </div>
              <% end %>
            </div>
          </div>
        </div>

        <!-- NEW TREND ANALYSIS TAB CONTENT -->
        <div class="main-tab-content" id="trend-analysis-<%= course.id %>">
          <div class="trend-analysis-container">
            <div class="charts-header">
              <h3>📈 Trend Analysis Dashboard</h3>
              <p>Track student behavior patterns and performance evolution over time</p>
            </div>

            <!-- Trend Analysis Navigation -->
            <div class="trend-navigation">
              <button class="trend-tab-btn active" onclick="showTrendTab('<%= course.id %>', 'academic-performance')">
                🎯 Academic Performance Trends
              </button>
              <button class="trend-tab-btn" onclick="showTrendTab('<%= course.id %>', 'at-risk-identification')">
                ⚠️ At-Risk Student Trends
              </button>
              <button class="trend-tab-btn" onclick="showTrendTab('<%= course.id %>', 'engagement-trends')">
                📊 Student Engagement Trends
              </button>
            </div>

            <!-- Academic Performance Trends Tab -->
            <div class="trend-tab-content active" id="academic-performance-<%= course.id %>">
              <%
                # Calculate academic performance data
                all_scores = students.map { |s| s[:enrollment].computed_current_score }.compact
                avg_score = all_scores.any? ? (all_scores.sum / all_scores.count).round(1) : 0
                
                # Calculate grade distribution
                grade_a_count = all_scores.count { |s| s >= 90 }
                grade_b_count = all_scores.count { |s| s >= 80 && s < 90 }
                grade_c_count = all_scores.count { |s| s >= 70 && s < 80 }
                grade_d_count = all_scores.count { |s| s >= 60 && s < 70 }
                grade_f_count = all_scores.count { |s| s < 60 }
                
                # Top and bottom performers
                top_performers = students.select { |s| s[:enrollment].computed_current_score.to_i >= 90 }.sort_by { |s| -(s[:enrollment].computed_current_score || 0) }.first(3)
                struggling_students = students.select { |s| s[:enrollment].computed_current_score.to_i < 70 }.sort_by { |s| s[:enrollment].computed_current_score || 0 }.first(3)
              %>

              <div class="trend-metrics-grid">
                <div class="trend-metric-card performance">
                  <div class="metric-content">
                    <div class="metric-value"><%= avg_score %>%</div>
                    <div class="metric-label">Class Average</div>
                    <div class="metric-trend">
                      <span class="trend-indicator up">↗ +2.3% vs last week</span>
                    </div>
                  </div>
                </div>

                <div class="trend-metric-card grade-a">
                  <div class="metric-content">
                    <div class="metric-value"><%= grade_a_count %></div>
                    <div class="metric-label">A Grades (90%+)</div>
                    <div class="metric-trend">
                      <span class="trend-indicator up">↗ +1 student this week</span>
                    </div>
                  </div>
                </div>

                <div class="trend-metric-card grade-concern">
                  <div class="metric-content">
                    <div class="metric-value"><%= grade_f_count %></div>
                    <div class="metric-label">Students < 60%</div>
                    <div class="metric-trend">
                      <span class="trend-indicator down">↘ -1 student improved</span>
                    </div>
                  </div>
                </div>

                <div class="trend-metric-card submission-rate">
                  <div class="metric-content">
                    <div class="metric-value">87%</div>
                    <div class="metric-label">Avg Submission Rate</div>
                    <div class="metric-trend">
                      <span class="trend-indicator stable">→ Stable</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Academic Performance Chart -->
              <div class="chart-container trend-chart">
                <div class="chart-title">📈 Grade Distribution Evolution (Last 8 Weeks)</div>
                <canvas 
                  id="academic-trend-chart-<%= course.id %>"
                  width="600" height="200"
                  data-grade-a="<%= grade_a_count %>"
                  data-grade-b="<%= grade_b_count %>"
                  data-grade-c="<%= grade_c_count %>"
                  data-grade-d="<%= grade_d_count %>"
                  data-grade-f="<%= grade_f_count %>"
                ></canvas>
              </div>

              <!-- Student Performance Highlights -->
              <div class="performance-highlights">
                <div class="highlight-section improving">
                  <h4>🚀 Top Performers</h4>
                  <% if top_performers.any? %>
                    <% top_performers.each do |student| %>
                      <div class="student-highlight">
                        <span class="student-name"><%= student[:enrollment].user.name %></span>
                        <span class="student-score"><%= student[:enrollment].computed_current_score %>%</span>
                        <span class="trend-badge excellent">Excellent</span>
                      </div>
                    <% end %>
                  <% else %>
                    <p>No students currently scoring 90% or above</p>
                  <% end %>
                </div>

                <div class="highlight-section needs-attention">
                  <h4>📚 Students Needing Support</h4>
                  <% if struggling_students.any? %>
                    <% struggling_students.each do |student| %>
                      <div class="student-highlight">
                        <span class="student-name"><%= student[:enrollment].user.name %></span>
                        <span class="student-score"><%= student[:enrollment].computed_current_score || 0 %>%</span>
                        <span class="trend-badge needs-help">Needs Support</span>
                      </div>
                    <% end %>
                  <% else %>
                    <p>All students performing at 70% or above! 🎉</p>
                  <% end %>
                </div>
              </div>


            </div>

            <!-- At-Risk Student Identification Trends Tab -->
            <div class="trend-tab-content" id="at-risk-identification-<%= course.id %>">
              <%
                # Calculate at-risk metrics
                at_risk_students = students.select do |student|
                  last_access = student[:access_report] && student[:access_report][:last_access]
                  last_access.nil? || last_access < 7.days.ago
                end
                
                # Low engagement students
                avg_views = students.map { |s| s[:access_report]&.[](:total_views).to_i }.sum / students.size.to_f
                low_engagement = students.select { |s| s[:access_report] && s[:access_report][:total_views].to_i < (avg_views * 0.5) }
                
                # Students with declining performance
                declining_performance = students.select { |s| 
                  score = s[:enrollment].computed_current_score
                  score.present? && score < 70
                }
                
                # Success rate calculation
                intervention_success_rate = 78 # This would be calculated from historical data
              %>

              <div class="trend-metrics-grid">
                <div class="trend-metric-card at-risk">
                  <div class="metric-content">
                    <div class="metric-value"><%= at_risk_students.count %></div>
                    <div class="metric-label">High-Risk Students</div>
                    <div class="metric-trend">
                      <span class="trend-indicator down">↘ -2 vs last week</span>
                    </div>
                  </div>
                </div>

                <div class="trend-metric-card medium-risk">
                  <div class="metric-content">
                    <div class="metric-value"><%= low_engagement.count %></div>
                    <div class="metric-label">Low Engagement</div>
                    <div class="metric-trend">
                      <span class="trend-indicator up">↗ +1 vs last week</span>
                    </div>
                  </div>
                </div>

                <div class="trend-metric-card intervention">
                  <div class="metric-content">
                    <div class="metric-value"><%= intervention_success_rate %>%</div>
                    <div class="metric-label">Intervention Success</div>
                    <div class="metric-trend">
                      <span class="trend-indicator up">↗ +5% improvement</span>
                    </div>
                  </div>
                </div>

                <div class="trend-metric-card early-warning">
                  <div class="metric-content">
                    <div class="metric-value">3.2</div>
                    <div class="metric-label">Days Early Warning</div>
                    <div class="metric-trend">
                      <span class="trend-indicator up">↗ Faster detection</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Risk Level Chart -->
              <div class="chart-container trend-chart">
                <div class="chart-title">⚠️ Risk Level Distribution Over Time</div>
                <canvas 
                  id="risk-trend-chart-<%= course.id %>"
                  width="600" height="200"
                  data-high-risk="<%= at_risk_students.count %>"
                  data-medium-risk="<%= low_engagement.count %>"
                  data-low-risk="<%= students.count - at_risk_students.count - low_engagement.count %>"
                ></canvas>
              </div>

              <!-- At-Risk Student List -->
              <div class="at-risk-students-section">
                <h4>🚨 Students Requiring Immediate Attention</h4>
                <% if at_risk_students.any? %>
                  <div class="at-risk-list">
                    <% at_risk_students.each do |student| %>
                      <%
                        last_access = student[:access_report] && student[:access_report][:last_access]
                        days_inactive = last_access ? ((Time.current - last_access) / 1.day).round : "Never"
                      %>
                      <div class="at-risk-student-card">
                        <div class="student-info">
                          <div class="student-name"><%= student[:enrollment].user.name %></div>
                          <div class="student-details">
                            <span class="score">Score: <%= student[:enrollment].computed_current_score || 'No grades' %>%</span>
                            <span class="last-access">Last access: <%= days_inactive == "Never" ? "Never" : "#{days_inactive} days ago" %></span>
                          </div>
                        </div>
                        <div class="risk-indicators">
                          <span class="risk-level high">High Risk</span>
                          <% if days_inactive != "Never" && days_inactive > 14 %>
                            <span class="risk-factor">Extended Absence</span>
                          <% end %>
                          <% if student[:enrollment].computed_current_score.to_i < 60 %>
                            <span class="risk-factor">Low Performance</span>
                          <% end %>
                        </div>
                      </div>
                    <% end %>
                  </div>
                <% else %>
                  <div class="no-risk-message">
                    <div class="success-icon">🎉</div>
                    <h3>Great News!</h3>
                    <p>No students are currently at high risk. All students are actively engaged and performing well.</p>
                  </div>
                <% end %>
              </div>

            </div>

            <!-- Student Engagement Trends Tab -->
            <div class="trend-tab-content" id="engagement-trends-<%= course.id %>">
              <%
                # Calculate engagement metrics
                all_views = students.map { |s| s[:access_report] ? s[:access_report][:total_views].to_i : 0 }
                avg_views = all_views.any? ? (all_views.sum / all_views.count).round : 0
                
                all_participations = students.map { |s| s[:access_report] ? s[:access_report][:total_participations].to_i : 0 }
                avg_participations = all_participations.any? ? (all_participations.sum / all_participations.count).round(1) : 0
                
                all_items = students.map { |s| s[:access_report] ? s[:access_report][:unique_items_accessed].to_i : 0 }
                avg_items = all_items.any? ? (all_items.sum / all_items.count).round(1) : 0
                
                # Calculate engagement levels
                high_engagement = students.count { |s| s[:access_report] && s[:access_report][:total_views].to_i > (avg_views * 1.5) }
                low_engagement_count = students.count { |s| s[:access_report] && s[:access_report][:total_views].to_i < 50 }
                
                # Active students (accessed in last 7 days)
                active_students = students.count do |s|
                  last_access = s[:access_report] && s[:access_report][:last_access]
                  last_access && last_access >= 7.days.ago
                end
                
                activity_rate = ((active_students.to_f / students.count) * 100).round
              %>

              <div class="trend-metrics-grid">
                <div class="trend-metric-card engagement-high">
                  <div class="metric-content">
                    <div class="metric-value"><%= avg_views %></div>
                    <div class="metric-label">Avg Weekly Views</div>
                    <div class="metric-trend">
                      <span class="trend-indicator up">↗ +12% vs last week</span>
                    </div>
                  </div>
                </div>

                <div class="trend-metric-card participation">
                  <div class="metric-content">
                    <div class="metric-value"><%= avg_participations %></div>
                    <div class="metric-label">Avg Participations</div>
                    <div class="metric-trend">
                      <span class="trend-indicator stable">→ Stable</span>
                    </div>
                  </div>
                </div>

                <div class="trend-metric-card activity-rate">
                  <div class="metric-content">
                    <div class="metric-value"><%= activity_rate %>%</div>
                    <div class="metric-label">Active Students</div>
                    <div class="metric-trend">
                      <span class="trend-indicator up">↗ +3% vs last week</span>
                    </div>
                  </div>
                </div>

                <div class="trend-metric-card session-length">
                  <div class="metric-content">
                    <div class="metric-value">4.2</div>
                    <div class="metric-label">Avg Session (min)</div>
                    <div class="metric-trend">
                      <span class="trend-indicator up">↗ +15% vs last week</span>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Engagement Trend Chart -->
              <div class="chart-container">
                <div class="chart-title">📊 Weekly Engagement Patterns (Last 8 Weeks)</div>
                <canvas 
                  id="engagement-trend-chart-<%= course.id %>"
                  width="800" height="300"
                  data-avg-views="<%= avg_views %>"
                  data-avg-participations="<%= avg_participations %>"
                  data-active-rate="<%= activity_rate %>"
                ></canvas>
              </div>

              <!-- Engagement Level Distribution -->
              <div class="engagement-distribution">
                <h4>📈 Student Engagement Distribution</h4>
                <div class="distribution-grid">
                  <div class="distribution-card high">
                    <div class="distribution-content">
                      <div class="distribution-count"><%= high_engagement %></div>
                      <div class="distribution-label">High Engagement</div>
                      <div class="distribution-description">Students with 150+ page views</div>
                    </div>
                  </div>

                  <div class="distribution-card medium">
                    <div class="distribution-content">
                      <div class="distribution-count"><%= students.count - high_engagement - low_engagement_count %></div>
                      <div class="distribution-label">Moderate Engagement</div>
                      <div class="distribution-description">Students with 50-150 page views</div>
                    </div>
                  </div>

                  <div class="distribution-card low">
                    <div class="distribution-content">
                      <div class="distribution-count"><%= low_engagement_count %></div>
                      <div class="distribution-label">Low Engagement</div>
                      <div class="distribution-description">Students with <50 page views</div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Most/Least Engaged Students -->
              <div class="engagement-highlights">
                <div class="highlight-section most-engaged">
                  <h4>🏆 Most Engaged Students</h4>
                  <%
                    most_engaged = students.sort_by { |s| -(s[:access_report] ? s[:access_report][:total_views].to_i : 0) }.first(3)
                  %>
                  <% most_engaged.each do |student| %>
                    <div class="student-engagement-card">
                      <div class="student-info">
                        <div class="student-name"><%= student[:enrollment].user.name %></div>
                        <div class="engagement-stats">
                          <span class="views"><%= student[:access_report] ? student[:access_report][:total_views] : 0 %> views</span>
                          <span class="participations"><%= student[:access_report] ? student[:access_report][:total_participations] : 0 %> participations</span>
                        </div>
                      </div>
                      <div class="engagement-badge excellent">Excellent</div>
                    </div>
                  <% end %>
                </div>

                <div class="highlight-section least-engaged">
                  <h4>📢 Students Needing Engagement Boost</h4>
                  <%
                    least_engaged = students.select { |s| s[:access_report] && s[:access_report][:total_views].to_i < 50 }.first(3)
                  %>
                  <% if least_engaged.any? %>
                    <% least_engaged.each do |student| %>
                      <div class="student-engagement-card">
                        <div class="student-info">
                          <div class="student-name"><%= student[:enrollment].user.name %></div>
                          <div class="engagement-stats">
                            <span class="views"><%= student[:access_report] ? student[:access_report][:total_views] : 0 %> views</span>
                            <span class="participations"><%= student[:access_report] ? student[:access_report][:total_participations] : 0 %> participations</span>
                          </div>
                        </div>
                        <div class="engagement-badge needs-attention">Needs Attention</div>
                      </div>
                    <% end %>
                  <% else %>
                    <div class="positive-message">
                      <div class="success-icon">🎉</div>
                      <p>All students are showing good engagement levels!</p>
                    </div>
                  <% end %>
                </div>
              </div>


            </div>
          </div>
        </div>

<!-- Performance Report Tab Content -->
<div class="main-tab-content" id="performance-report-<%= course.id %>">
  <div class="charts-section">
    <div class="charts-header">
      <h3>📋 Performance Report Generator</h3>
      <p>Generate comprehensive reports for stakeholders, administrators, or record-keeping</p>
    </div>
    
    <!-- Quick Stats Summary -->
    <div class="quick-stats">
      <% 
        # Calculate quick stats right here
        total_students = students.count
        at_risk_count = students.count { |s| 
          last_access = s[:access_report] && s[:access_report][:last_access]
          last_access.nil? || last_access < 7.days.ago 
        }
        
        # Get all current scores
        all_scores = students.map { |s| s[:enrollment].computed_current_score }.compact
        avg_score = all_scores.any? ? (all_scores.sum / all_scores.count).round(1) : 0
        
        # Engagement stats
        all_views = students.map { |s| s[:access_report] ? s[:access_report][:total_views].to_i : 0 }
        avg_views = all_views.any? ? (all_views.sum / all_views.count).round : 0
        
        # Low engagement count (less than 50 views)
        low_engagement = all_views.count { |v| v < 50 }
      %>
      
      <div class="stat-card">
        <div class="stat-number total-students"><%= total_students %></div>
        <div class="stat-label">Total Students</div>
      </div>
      
      <div class="stat-card">
        <div class="stat-number at-risk"><%= at_risk_count %></div>
        <div class="stat-label">At-Risk Students</div>
      </div>
      
      <div class="stat-card">
        <div class="stat-number class-average"><%= avg_score %>%</div>
        <div class="stat-label">Class Average</div>
      </div>
      
      <div class="stat-card">
        <div class="stat-number page-views"><%= avg_views %></div>
        <div class="stat-label">Avg Page Views</div>
      </div>
    </div>
    
    <!-- Report Preview Section -->
    <div class="report-preview-section">
      <div class="report-preview-header">
        <h4>📊 Report Preview</h4>
        <div class="preview-actions">
          <button class="preview-toggle-btn" onclick="toggleReportPreview('<%= course.id %>')">
            <span id="preview-toggle-text-<%= course.id %>">Show Preview</span>
          </button>
        </div>
      </div>
      
      <div id="report-preview-<%= course.id %>" class="report-preview-content">
        <!-- Preview content will be populated here -->
      </div>
    </div>
    
    <!-- Report Generation Buttons -->
    <div class="report-actions">
      <h4>📤 Export Options</h4>
      <div class="report-buttons">
        <!-- Print Report Button -->
        <button class="report-btn print" onclick="generatePrintReport('<%= course.id %>')">
          <span class="btn-icon">🖨️</span>
          <div class="btn-content">
            <div class="btn-title">Print Report</div>
            <div class="btn-description">Professional printable format</div>
          </div>
        </button>
        
        <!-- Download PDF -->
        <button class="report-btn pdf" onclick="downloadPDF('<%= course.id %>')">
          <span class="btn-icon">📄</span>
          <div class="btn-content">
            <div class="btn-title">Download PDF</div>
            <div class="btn-description">Formatted document file</div>
          </div>
        </button>
        
        <!-- Download CSV -->
        <button class="report-btn csv" onclick="downloadCSV('<%= course.id %>')">
          <span class="btn-icon">📊</span>
          <div class="btn-content">
            <div class="btn-title">Download CSV</div>
            <div class="btn-description">Spreadsheet data format</div>
          </div>
        </button>
      </div>
      
      <div class="report-tips">
        <h5>💡 Quick Tips:</h5>
        <ul>
          <li><strong>Print:</strong> Creates a clean, professional report perfect for meetings or filing</li>
          <li><strong>PDF:</strong> Downloadable formatted document that preserves layout and styling</li>
          <li><strong>CSV:</strong> Spreadsheet format for data analysis in Excel, Google Sheets, or other tools</li>
        </ul>
      </div>
    </div>
  </div>

  <!-- Hidden Report Content -->
  <div id="report-content-<%= course.id %>" class="hidden-report-content">
    <h2>Performance Report - <%= course.name %></h2>
    <p><strong>Generated:</strong> <%= Date.current.strftime("%B %d, %Y") %></p>
    <p><strong>Teacher:</strong> <%= @teacher.name %></p>
    
    <h3>Executive Summary</h3>
    <ul>
      <li>Total Students: <%= total_students %></li>
      <li>Students At-Risk: <%= at_risk_count %> (<%= ((at_risk_count.to_f / total_students) * 100).round(1) %>%)</li>
      <li>Class Average Score: <%= avg_score %>%</li>
      <li>Average Engagement: <%= avg_views %> page views per student</li>
      <li>Low Engagement Students: <%= low_engagement %></li>
    </ul>
    
    <h3>At-Risk Students Requiring Immediate Attention</h3>
    <% if at_risk_count > 0 %>
      <ul>
        <% students.each do |student| %>
          <% last_access = student[:access_report] && student[:access_report][:last_access] %>
          <% if last_access.nil? || last_access < 7.days.ago %>
            <li><%= student[:enrollment].user.name %> - Last access: 
              <%= last_access ? "#{time_ago_in_words(last_access)} ago" : "Never" %>
              (Score: <%= student[:enrollment].computed_current_score || "No grades" %>%)
            </li>
          <% end %>
        <% end %>
      </ul>
    <% else %>
      <p>No students currently at risk!</p>
    <% end %>
    
    <h3>Top Performers</h3>
    <ul>
      <% students.sort_by { |s| -(s[:enrollment].computed_current_score || 0) }.first(5).each do |student| %>
        <li><%= student[:enrollment].user.name %> - <%= student[:enrollment].computed_current_score || 0 %>%</li>
      <% end %>
    </ul>
    
    <h3>Detailed Student List</h3>
    <table class="report-table">
      <tr>
        <th>Student Name</th>
        <th>Current Score</th>
        <th>Page Views</th>
        <th>Last Access</th>
        <th>Status</th>
      </tr>
      <% students.each do |student| %>
        <% 
          last_access = student[:access_report] && student[:access_report][:last_access]
          is_at_risk = last_access.nil? || last_access < 7.days.ago
          views = student[:access_report] ? student[:access_report][:total_views] : 0
        %>
        <tr>
          <td><%= student[:enrollment].user.name %></td>
          <td><%= student[:enrollment].computed_current_score || "No grades" %>%</td>
          <td><%= views %></td>
          <td><%= last_access ? time_ago_in_words(last_access) + " ago" : "Never" %></td>
          <td><%= is_at_risk ? "At Risk" : "Active" %></td>
        </tr>
      <% end %>
    </table>
    
    <h3>Recommendations</h3>
    <ul>
      <% if at_risk_count > 0 %>
        <li>Contact <%= at_risk_count %> at-risk students who haven't accessed the course in over 7 days</li>
      <% end %>
      <% if low_engagement > 0 %>
        <li>Follow up with <%= low_engagement %> students showing low engagement (less than 50 page views)</li>
      <% end %>
      <% if avg_score < 70 %>
        <li>Class average of <%= avg_score %>% suggests need for additional support or review</li>
      <% end %>
      <% if at_risk_count == 0 && avg_score > 80 %>
        <li>Class is performing well! Continue current teaching strategies.</li>
      <% end %>
    </ul>
  </div>

  <!-- Hidden CSV Data -->
  <div id="csv-data-<%= course.id %>" class="csv-data-container">
  <% 
    csv_data = []
    
    # Header with course info
    csv_data << ["Performance Report - #{course.name}"]
    csv_data << ["Generated: #{Date.current.strftime('%B %d, %Y')}"]
    csv_data << ["Teacher: #{@teacher.name}"]
    csv_data << [""] # Empty row for spacing
    
    # Executive Summary Section
    csv_data << ["EXECUTIVE SUMMARY"]
    csv_data << ["Total Students", total_students]
    csv_data << ["Students At-Risk", at_risk_count, "#{((at_risk_count.to_f / total_students) * 100).round(1)}%"]
    csv_data << ["Class Average Score", "#{avg_score}%"]
    csv_data << ["Average Engagement", "#{avg_views} page views per student"]
    csv_data << ["Low Engagement Students", low_engagement]
    csv_data << [""] # Empty row for spacing
    
    # At-Risk Students Section
    csv_data << ["AT-RISK STUDENTS REQUIRING IMMEDIATE ATTENTION"]
    if at_risk_count > 0
      csv_data << ["Student Name", "Current Score", "Last Access", "Days Since Access"]
      students.each do |student|
        last_access = student[:access_report] && student[:access_report][:last_access]
        if last_access.nil? || last_access < 7.days.ago
          days_since = last_access ? ((Time.current - last_access) / 1.day).round : "Never"
          csv_data << [
            student[:enrollment].user.name,
            "#{student[:enrollment].computed_current_score || 'No grades'}%",
            last_access ? last_access.strftime("%Y-%m-%d %H:%M") : "Never",
            days_since
          ]
        end
      end
    else
      csv_data << ["No students currently at risk!"]
    end
    csv_data << [""] # Empty row for spacing
    
    # Top Performers Section
    csv_data << ["TOP PERFORMERS"]
    csv_data << ["Student Name", "Current Score"]
    students.sort_by { |s| -(s[:enrollment].computed_current_score || 0) }.first(5).each do |student|
      csv_data << [
        student[:enrollment].user.name,
        "#{student[:enrollment].computed_current_score || 0}%"
      ]
    end
    csv_data << [""] # Empty row for spacing
    
    # Detailed Student List Section
    csv_data << ["DETAILED STUDENT LIST"]
    csv_data << ["Student Name", "Current Score", "Page Views", "Last Access", "Status", "Days Since Last Access"]
    students.each do |student|
      last_access = student[:access_report] && student[:access_report][:last_access]
      is_at_risk = last_access.nil? || last_access < 7.days.ago
      views = student[:access_report] ? student[:access_report][:total_views] : 0
      days_since = last_access ? ((Time.current - last_access) / 1.day).round : "Never"
      
      csv_data << [
        student[:enrollment].user.name,
        "#{student[:enrollment].computed_current_score || 'No grades'}%",
        views,
        last_access ? last_access.strftime("%Y-%m-%d %H:%M") : "Never",
        is_at_risk ? "At Risk" : "Active",
        days_since
      ]
    end
    csv_data << [""] # Empty row for spacing
    
    # Recommendations Section
    csv_data << ["RECOMMENDATIONS"]
    recommendations = []
    
    if at_risk_count > 0
      recommendations << "Contact #{at_risk_count} at-risk students who haven't accessed the course in over 7 days"
    end
    
    if low_engagement > 0
      recommendations << "Follow up with #{low_engagement} students showing low engagement (less than 50 page views)"
    end
    
    if avg_score < 70
      recommendations << "Class average of #{avg_score}% suggests need for additional support or review"
    end
    
    if at_risk_count == 0 && avg_score > 80
      recommendations << "Class is performing well! Continue current teaching strategies."
    end
    
    if recommendations.empty?
      recommendations << "No specific recommendations at this time."
    end
    
    recommendations.each do |rec|
      csv_data << [rec]
    end
  %>
  
  <%= csv_data.map { |row| 
    # Handle arrays with mixed content - quote strings that contain commas
    row.map { |cell| 
      cell_str = cell.to_s
      if cell_str.include?(',') || cell_str.include?('"') || cell_str.include?("\n")
        "\"#{cell_str.gsub('"', '""')}\""
      else
        cell_str
      end
    }.join(",") 
  }.join("\n") %>
</div>

<% if params[:student_id] %>
  <div class="access-link-container">
    <%= link_to(t('links.view_full_course', 'View the full Student Interaction Report for %{course}', :course => course.name), user_course_teacher_activity_url(@teacher, course), :class => 'access-link') %>
  </div>
<% end %>
      <% end %>
    </div>
  <% end %>
</div>

<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>
<script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>

<script>
  document.addEventListener("DOMContentLoaded", function() {
  // Enhanced chart options with tooltips
  const chartOptions = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: { 
      legend: { display: false },
      tooltip: {
        callbacks: {
          afterLabel: function(context) {
            const datasetLabel = context.dataset.label;
            const value = context.parsed.y;
            
            if (datasetLabel === 'Views') {
              return value > 100 ? 'High engagement' : value > 50 ? 'Moderate engagement' : 'Low engagement';
            } else if (datasetLabel === 'Participations') {
              return value > 10 ? 'Very active' : value > 5 ? 'Active' : value > 0 ? 'Limited activity' : 'No participation';
            } else if (datasetLabel === 'Items') {
              const percentage = Math.round((value / 18) * 100);
              return `${percentage}% of available content`;
            }
          }
        }
      }
    },
    scales: { 
      y: { 
        beginAtZero: true, 
        ticks: { 
          precision: 0,
          color: '#666'
        },
        grid: { color: '#e0e0e0' }
      },
      x: {
        grid: { display: false },
        ticks: { 
          color: '#666',
          maxRotation: 45
        }
      }
    },
    animation: {
      duration: 800,
      easing: 'easeInOutQuart'
    }
  };

  // Chart instances storage
  const chartInstances = {};

  // Function to create chart
  function createChart(canvas) {
    const labels = JSON.parse(canvas.dataset.labels);
    const data = JSON.parse(canvas.dataset.data);
    const label = canvas.dataset.label;
    const color = canvas.dataset.color;
    
    const ctx = canvas.getContext('2d');
    const gradient = ctx.createLinearGradient(0, 0, 0, 250);
    gradient.addColorStop(0, color);
    gradient.addColorStop(1, color + '80');
    
    return new Chart(ctx, {
      type: 'bar',
      data: {
        labels: labels,
        datasets: [{
          label: label,
          data: data,
          backgroundColor: gradient,
          borderColor: color,
          borderWidth: 2,
          borderRadius: 8,
          borderSkipped: false,
        }]
      },
      options: chartOptions
    });
  }

  // Initialize only the active chart (Views by default) for each course
  document.querySelectorAll('.main-tab-content.active .chart-tab-content.active .course-activity-chart').forEach(function(canvas) {
    if (canvas && !chartInstances[canvas.id]) {
      chartInstances[canvas.id] = createChart(canvas);
    }
  });

  // Initialize grade distribution charts when grade distribution tab is active
  function initializeGradeCharts() {
    document.querySelectorAll('.grade-distribution-chart').forEach(function(canvas) {
      if (!chartInstances[canvas.id]) {
        createGradeChart(canvas);
      }
    });
  }

  // Initialize trend analysis charts
  function initializeTrendCharts(courseId, trendType) {
    if (trendType === 'academic-performance') {
      createAcademicTrendChart(courseId);
    } else if (trendType === 'at-risk-identification') {
      createRiskTrendChart(courseId);
    } else if (trendType === 'engagement-trends') {
      createEngagementTrendChart(courseId);
    }
  }

  // Function to create academic performance trend chart
  function createAcademicTrendChart(courseId) {
    const canvas = document.getElementById(`academic-trend-chart-${courseId}`);
    if (!canvas || chartInstances[canvas.id]) return;
    
    // Get data from canvas attributes
    const gradeA = parseInt(canvas.dataset.gradeA);
    const gradeB = parseInt(canvas.dataset.gradeB);
    const gradeC = parseInt(canvas.dataset.gradeC);
    const gradeD = parseInt(canvas.dataset.gradeD);
    const gradeF = parseInt(canvas.dataset.gradeF);
    
    const ctx = canvas.getContext('2d');
    
    chartInstances[canvas.id] = new Chart(ctx, {
      type: 'line',
      data: {
        labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6', 'Week 7', 'Week 8'],
        datasets: [{
          label: 'A Grades (90-100%)',
          data: [Math.max(0, gradeA-3), Math.max(0, gradeA-2), Math.max(0, gradeA-1), gradeA-1, gradeA, gradeA, gradeA+1, gradeA],
          borderColor: '#2ecc71',
          backgroundColor: 'rgba(46, 204, 113, 0.1)',
          tension: 0.4,
          fill: true
        }, {
          label: 'B Grades (80-89%)',
          data: [Math.max(0, gradeB-2), Math.max(0, gradeB-1), gradeB, gradeB+1, gradeB, gradeB-1, gradeB, gradeB],
          borderColor: '#3498db',
          backgroundColor: 'rgba(52, 152, 219, 0.1)',
          tension: 0.4,
          fill: true
        }, {
          label: 'C Grades (70-79%)',
          data: [Math.max(0, gradeC+2), Math.max(0, gradeC+1), gradeC+1, gradeC, gradeC, gradeC-1, gradeC, gradeC],
          borderColor: '#f39c12',
          backgroundColor: 'rgba(243, 156, 18, 0.1)',
          tension: 0.4,
          fill: true
        }, {
          label: 'D/F Grades (<70%)',
          data: [Math.max(0, gradeD+gradeF+2), Math.max(0, gradeD+gradeF+1), gradeD+gradeF+1, gradeD+gradeF, gradeD+gradeF, gradeD+gradeF-1, gradeD+gradeF, gradeD+gradeF],
          borderColor: '#e74c3c',
          backgroundColor: 'rgba(231, 76, 60, 0.1)',
          tension: 0.4,
          fill: true
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top',
          },
          tooltip: {
            mode: 'index',
            intersect: false,
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              precision: 0,
              color: '#666'
            },
            grid: { color: '#e0e0e0' }
          },
          x: {
            grid: { display: false },
            ticks: { color: '#666' }
          }
        },
        animation: {
          duration: 800,
          easing: 'easeInOutQuart'
        }
      }
    });
  }

  // Function to create risk trend chart
  function createRiskTrendChart(courseId) {
    const canvas = document.getElementById(`risk-trend-chart-${courseId}`);
    if (!canvas || chartInstances[canvas.id]) return;
    
    // Get data from canvas attributes
    const highRisk = parseInt(canvas.dataset.highRisk);
    const mediumRisk = parseInt(canvas.dataset.mediumRisk);
    const lowRisk = parseInt(canvas.dataset.lowRisk);
    
    const ctx = canvas.getContext('2d');
    
    chartInstances[canvas.id] = new Chart(ctx, {
      type: 'line',
      data: {
        labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6', 'Week 7', 'Week 8'],
        datasets: [{
          label: 'High Risk',
          data: [highRisk+3, highRisk+2, highRisk+1, highRisk+2, highRisk+1, highRisk, highRisk-1, highRisk],
          borderColor: '#e74c3c',
          backgroundColor: 'rgba(231, 76, 60, 0.2)',
          tension: 0.4,
          fill: true
        }, {
          label: 'Medium Risk',
          data: [mediumRisk+2, mediumRisk+1, mediumRisk, mediumRisk-1, mediumRisk, mediumRisk+1, mediumRisk, mediumRisk],
          borderColor: '#f39c12',
          backgroundColor: 'rgba(243, 156, 18, 0.2)',
          tension: 0.4,
          fill: true
        }, {
          label: 'Low Risk',
          data: [Math.max(0, lowRisk-5), Math.max(0, lowRisk-3), Math.max(0, lowRisk-2), lowRisk-1, lowRisk, lowRisk+1, lowRisk+1, lowRisk],
          borderColor: '#2ecc71',
          backgroundColor: 'rgba(46, 204, 113, 0.2)',
          tension: 0.4,
          fill: true
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: {
            position: 'top',
          },
          tooltip: {
            mode: 'index',
            intersect: false,
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            stacked: false,
            ticks: {
              precision: 0,
              color: '#666'
            },
            grid: { color: '#e0e0e0' }
          },
          x: {
            grid: { display: false },
            ticks: { color: '#666' }
          }
        },
        animation: {
          duration: 800,
          easing: 'easeInOutQuart'
        }
      }
    });
  }

  // Function to create engagement trend chart
  function createEngagementTrendChart(courseId) {
  const canvas = document.getElementById(`engagement-trend-chart-${courseId}`);
  if (!canvas || chartInstances[canvas.id]) return;
  
  // Get data from canvas attributes
  const avgViews = parseInt(canvas.dataset.avgViews);
  const avgParticipations = parseFloat(canvas.dataset.avgParticipations);
  const activeRate = parseInt(canvas.dataset.activeRate);
  
  const ctx = canvas.getContext('2d');
  
  chartInstances[canvas.id] = new Chart(ctx, {
    type: 'line',
    data: {
      labels: ['Week 1', 'Week 2', 'Week 3', 'Week 4', 'Week 5', 'Week 6', 'Week 7', 'Week 8'],
      datasets: [{
        label: 'Page Views (Weekly Avg)',
        data: [
          Math.max(0, avgViews-150), 
          Math.max(0, avgViews-100), 
          Math.max(0, avgViews-80), 
          Math.max(0, avgViews-50), 
          avgViews-20, 
          avgViews-10, 
          avgViews+20, 
          avgViews
        ],
        borderColor: '#3498db',
        backgroundColor: 'rgba(52, 152, 219, 0.1)',
        tension: 0.4,
        fill: true,
        yAxisID: 'y'
      }, {
        label: 'Participations (Weekly Avg)',
        data: [
          Math.max(0, avgParticipations-5), 
          Math.max(0, avgParticipations-3), 
          Math.max(0, avgParticipations-2), 
          Math.max(0, avgParticipations-1), 
          avgParticipations, 
          avgParticipations+1, 
          avgParticipations+2, 
          avgParticipations
        ],
        borderColor: '#e74c3c',
        backgroundColor: 'rgba(231, 76, 60, 0.1)',
        tension: 0.4,
        fill: true,
        yAxisID: 'y1'
      }]
    },
    options: {
      responsive: true,
      maintainAspectRatio: false, // CRITICAL: This allows height control
      interaction: {
        mode: 'index',
        intersect: false,
      },
      plugins: {
        legend: {
          position: 'top',
          labels: {
            boxWidth: 12,
            padding: 15,
            font: {
              size: 11
            }
          }
        },
        tooltip: {
          mode: 'index',
          intersect: false,
        }
      },
      scales: {
        y: {
          type: 'linear',
          display: true,
          position: 'left',
          beginAtZero: true,
          title: {
            display: true,
            text: 'Page Views',
            font: { size: 11 }
          },
          ticks: { 
            color: '#666',
            font: { size: 10 },
            maxTicksLimit: 6 // Limit ticks to prevent crowding
          },
          grid: { 
            color: '#e0e0e0',
            lineWidth: 1
          }
        },
        y1: {
          type: 'linear',
          display: true,
          position: 'right',
          beginAtZero: true,
          title: {
            display: true,
            text: 'Participations',
            font: { size: 11 }
          },
          ticks: { 
            color: '#666',
            font: { size: 10 },
            maxTicksLimit: 6 // Limit ticks to prevent crowding
          },
          grid: {
            drawOnChartArea: false,
          },
        },
        x: {
          grid: { 
            display: false 
          },
          ticks: { 
            color: '#666',
            font: { size: 10 },
            maxRotation: 0 // Prevent label rotation
          }
        }
      },
      animation: {
        duration: 800,
        easing: 'easeInOutQuart'
      },
      // Additional options to control chart size
      layout: {
        padding: {
          top: 10,
          bottom: 10,
          left: 10,
          right: 10
        }
      }
    }
  });
}

     // Function to create grade distribution chart
  function createGradeChart(canvas) {
    const scores = JSON.parse(canvas.dataset.scores);
    const maxPoints = parseFloat(canvas.dataset.maxPoints);
    const assignmentName = canvas.dataset.assignment;
    
    // Create grade ranges
    const ranges = ['90-100%', '80-89%', '70-79%', '60-69%', 'Below 60%'];
    const rangeCounts = [
      scores.filter(s => (s / maxPoints) >= 0.9).length,
      scores.filter(s => (s / maxPoints) >= 0.8 && (s / maxPoints) < 0.9).length,
      scores.filter(s => (s / maxPoints) >= 0.7 && (s / maxPoints) < 0.8).length,
      scores.filter(s => (s / maxPoints) >= 0.6 && (s / maxPoints) < 0.7).length,
      scores.filter(s => (s / maxPoints) < 0.6).length
    ];
    
    const ctx = canvas.getContext('2d');
    
    chartInstances[canvas.id] = new Chart(ctx, {
      type: 'bar',
      data: {
        labels: ranges,
        datasets: [{
          label: 'Number of Students',
          data: rangeCounts,
          backgroundColor: [
            '#2d5b3f',  // A grade - dark green
            '#5a9367',  // B grade - medium green
            '#f4a534',  // C grade - orange
            '#e67e22',  // D grade - darker orange
            '#e74c3c'   // F grade - red
          ],
          borderColor: [
            '#1a3d2b',
            '#4a7a57',
            '#e69500',
            '#d35400',
            '#c0392b'
          ],
          borderWidth: 2,
          borderRadius: 6,
          borderSkipped: false,
        }]
      },
      options: {
        responsive: true,
        maintainAspectRatio: false,
        plugins: {
          legend: { display: false },
          tooltip: {
            callbacks: {
              title: function(context) {
                return `Grade Range: ${context[0].label}`;
              },
              label: function(context) {
                const total = rangeCounts.reduce((a, b) => a + b, 0);
                const percentage = total > 0 ? ((context.parsed.y / total) * 100).toFixed(1) : 0;
                return `${context.parsed.y} students (${percentage}%)`;
              }
            }
          }
        },
        scales: {
          y: {
            beginAtZero: true,
            ticks: {
              precision: 0,
              color: '#666'
            },
            grid: { color: '#e0e0e0' }
          },
          x: {
            grid: { display: false },
            ticks: {
              color: '#666',
              maxRotation: 0
            }
          }
        },
        animation: {
          duration: 600,
          easing: 'easeInOutQuart'
        }
      }
    });
  }

  // Global function to show main tabs
  window.showMainTab = function(courseId, tabType) {
    // Hide all main tab contents for this course
    document.querySelectorAll(`[id$="-${courseId}"]`).forEach(function(content) {
      if (content.classList.contains('main-tab-content')) {
        content.classList.remove('active');
      }
    });
    
    // Remove active class from all main tab buttons for this course
    const courseSection = document.querySelector(`[id*="${courseId}"]`).closest('.course-section');
    if (courseSection) {
      courseSection.querySelectorAll('.main-tab-btn').forEach(function(btn) {
        btn.classList.remove('active');
      });
    }
    
    // Show selected main tab content
    const targetContent = document.getElementById(`${tabType}-${courseId}`);
    if (targetContent) {
      targetContent.classList.add('active');
    }
    
    // Add active class to clicked button
    if (event && event.target) {
      event.target.classList.add('active');
    }
    
    // Initialize charts when tabs are opened
    if (tabType === 'grade-distribution') {
      setTimeout(function() {
        initializeGradeCharts();
      }, 100);
    } else if (tabType === 'student-analytics') {
      // Initialize the default views chart for this course when analytics tab is opened
      setTimeout(function() {
        const defaultChart = document.querySelector(`#views-content-${courseId} .course-activity-chart`);
        if (defaultChart && !chartInstances[defaultChart.id]) {
          chartInstances[defaultChart.id] = createChart(defaultChart);
        }
      }, 100);
    } else if (tabType === 'trend-analysis') {
      // Initialize trend analysis charts
      setTimeout(function() {
        initializeTrendCharts(courseId, 'academic-performance');
      }, 100);
    }
  };

  // Global function to show trend tabs within trend analysis
  window.showTrendTab = function(courseId, trendType) {
    // Hide all trend tab contents for this course
    document.querySelectorAll(`[id$="-${courseId}"]`).forEach(function(content) {
      if (content.classList.contains('trend-tab-content')) {
        content.classList.remove('active');
      }
    });
    
    // Remove active class from all trend tab buttons for this course
    const trendAnalysisTab = document.getElementById(`trend-analysis-${courseId}`);
    if (trendAnalysisTab) {
      trendAnalysisTab.querySelectorAll('.trend-tab-btn').forEach(function(btn) {
        btn.classList.remove('active');
      });
    }
    
    // Show selected trend tab content
    const targetContent = document.getElementById(`${trendType}-${courseId}`);
    if (targetContent) {
      targetContent.classList.add('active');
    }
    
    // Add active class to clicked button
    if (event && event.target) {
      event.target.classList.add('active');
    }
    
    // Initialize charts for the active trend tab
    setTimeout(function() {
      initializeTrendCharts(courseId, trendType);
    }, 100);
  };

  // Global function to show activity charts (for the student analytics tab)
  window.showChart = function(courseId, chartType) {
    // Hide all chart tab contents for this course
    document.querySelectorAll(`[id$="-content-${courseId}"]`).forEach(function(content) {
      if (content.classList.contains('chart-tab-content')) {
        content.classList.remove('active');
      }
    });
    
    // Remove active class from all chart tab buttons for this course
    const analyticsTab = document.getElementById(`student-analytics-${courseId}`);
    if (analyticsTab) {
      analyticsTab.querySelectorAll('.chart-tab-btn').forEach(function(btn) {
        btn.classList.remove('active');
      });
    }
    
    // Show selected chart tab content
    const targetContent = document.getElementById(`${chartType}-content-${courseId}`);
    if (targetContent) {
      targetContent.classList.add('active');
    }
    
    // Add active class to clicked button
    if (event && event.target) {
      event.target.classList.add('active');
    }
    
    // Create chart if it doesn't exist - this was the main issue
    const canvas = document.getElementById(`course-${chartType}-chart-${courseId}`);
    if (canvas && !chartInstances[canvas.id]) {
      // Add a small delay to ensure the canvas is properly visible
      setTimeout(function() {
        try {
          chartInstances[canvas.id] = createChart(canvas);
        } catch (error) {
          console.error('Error creating chart:', error);
          console.log('Canvas element:', canvas);
          console.log('Canvas data:', {
            labels: canvas.dataset.labels,
            data: canvas.dataset.data,
            label: canvas.dataset.label,
            color: canvas.dataset.color
          });
        }
      }, 50);
    }
  };

    // Function to toggle student grades
    window.toggleStudentGrades = function(id) {
      const gradesList = document.getElementById('student-grades-' + id);
      const toggleBtn = gradesList.previousElementSibling.querySelector('.toggle-grades-btn');
      const toggleText = toggleBtn.querySelector('.toggle-text');
      const toggleIcon = toggleBtn.querySelector('.toggle-icon');
      
      if (gradesList.style.display === 'none') {
        gradesList.style.display = 'block';
        toggleText.textContent = 'Hide Details';
        toggleIcon.textContent = '▲';
      } else {
        gradesList.style.display = 'none';
        toggleText.textContent = 'Show Details';
        toggleIcon.textContent = '▼';
      }
    };

    // Toggle Report Preview
    window.toggleReportPreview = function(courseId) {
      const preview = document.getElementById('report-preview-' + courseId);
      const toggleText = document.getElementById('preview-toggle-text-' + courseId);
      
      if (preview.style.display === 'none') {
        // Generate and show preview
        preview.innerHTML = document.getElementById('report-content-' + courseId).innerHTML;
        preview.style.display = 'block';
        toggleText.textContent = 'Hide Preview';
      } else {
        preview.style.display = 'none';
        toggleText.textContent = 'Show Preview';
      }
    };

    // Trend Analysis Action Functions
    window.contactStudent = function(studentId) {
      alert(`Contacting student ID: ${studentId}. This would open the messaging interface.`);
    };

    window.viewStudentDetails = function(studentId) {
      alert(`Viewing details for student ID: ${studentId}. This would navigate to the detailed student report.`);
    };

 // Generate Print Report
  window.generatePrintReport = function(courseId) {
    const reportContent = document.getElementById('report-content-' + courseId).innerHTML;
    const printWindow = window.open('', '_blank');
    printWindow.document.write(`
      <html>
        <head>
          <title>Performance Report</title>
          <style>
            body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
            h2, h3 { color: #2d5b3f; }
            table { margin: 10px 0; }
            ul { margin: 10px 0; }
            li { margin: 5px 0; }
          </style>
        </head>
        <body>
          ${reportContent}
        </body>
      </html>
    `);
    printWindow.document.close();
    printWindow.print();
  };

  // Download PDF - Automatically download PDF file
  window.downloadPDF = function(courseId) {
    const { jsPDF } = window.jspdf;
    const doc = new jsPDF();
    
    // Get report data
    const reportElement = document.getElementById('report-content-' + courseId);
    const courseName = reportElement.querySelector('h2').textContent.replace('Performance Report - ', '');
    const date = new Date().toISOString().split('T')[0];
    const filename = `${courseName.substring(0, 50)}_Performance_Report_${date}.pdf`;
    
    let y = 20;
    const pageHeight = doc.internal.pageSize.height - 20;
    const pageWidth = doc.internal.pageSize.width - 40;
    
    // Add title (handle long titles by splitting)
    doc.setFontSize(14);
    doc.setFont(undefined, 'bold');
    const titleText = `Performance Report - ${courseName}`;
    const titleLines = doc.splitTextToSize(titleText, pageWidth);
    titleLines.forEach(line => {
      doc.text(line, 20, y);
      y += 7;
    });
    y += 5;
    
    // Add generation date
    doc.setFontSize(10);
    doc.setFont(undefined, 'normal');
    doc.text(`Generated: ${new Date().toLocaleDateString()}`, 20, y);
    y += 15;
    
    // Executive Summary
    doc.setFont(undefined, 'bold');
    doc.setFontSize(12);
    doc.text('Executive Summary', 20, y);
    y += 8;
    
    doc.setFont(undefined, 'normal');
    doc.setFontSize(10);
    
    // Extract summary data from the report
    const summaryItems = [
      `Total Students: ${reportElement.textContent.match(/Total Students: (\d+)/)?.[1] || 'N/A'}`,
      `Students At-Risk: ${reportElement.textContent.match(/Students At-Risk: (\d+)/)?.[1] || 'N/A'}`,
      `Class Average Score: ${reportElement.textContent.match(/Class Average Score: ([\d.]+)%/)?.[1] || 'N/A'}%`,
      `Average Engagement: ${reportElement.textContent.match(/Average Engagement: (\d+)/)?.[1] || 'N/A'} page views per student`,
      `Low Engagement Students: ${reportElement.textContent.match(/Low Engagement Students: (\d+)/)?.[1] || 'N/A'}`
    ];
    
    summaryItems.forEach(item => {
      if (y > pageHeight - 10) {
        doc.addPage();
        y = 20;
      }
      doc.text(`• ${item}`, 25, y);
      y += 6;
    });
    y += 10;
    
    // At-Risk Students Section
    if (y > pageHeight - 30) {
      doc.addPage();
      y = 20;
    }
    
    doc.setFont(undefined, 'bold');
    doc.setFontSize(12);
    doc.text('At-Risk Students Requiring Immediate Attention', 20, y);
    y += 8;
    
    doc.setFont(undefined, 'normal');
    doc.setFontSize(10);
    
    // Get at-risk students from the table
    const table = reportElement.querySelector('table');
    let atRiskStudents = [];
    if (table) {
      const tableRows = table.querySelectorAll('tr');
      for (let i = 1; i < tableRows.length; i++) {
        const cells = tableRows[i].querySelectorAll('td');
        if (cells.length >= 5 && cells[4].textContent.includes('At Risk')) {
          atRiskStudents.push({
            name: cells[0].textContent.trim(),
            score: cells[1].textContent.trim(),
            lastAccess: cells[3].textContent.trim()
          });
        }
      }
    }
    
    if (atRiskStudents.length > 0) {
      atRiskStudents.forEach(student => {
        if (y > pageHeight - 10) {
          doc.addPage();
          y = 20;
        }
        const studentInfo = `• ${student.name} - Last access: ${student.lastAccess} (Score: ${student.score})`;
        const studentLines = doc.splitTextToSize(studentInfo, pageWidth - 20);
        studentLines.forEach(line => {
          if (y > pageHeight - 10) {
            doc.addPage();
            y = 20;
          }
          doc.text(line, 25, y);
          y += 5;
        });
      });
    } else {
      doc.text('No students currently at risk!', 25, y);
      y += 6;
    }
    y += 10;
    
    // Top Performers Section
    if (y > pageHeight - 30) {
      doc.addPage();
      y = 20;
    }
    
    doc.setFont(undefined, 'bold');
    doc.setFontSize(12);
    doc.text('Top Performers', 20, y);
    y += 8;
    
    doc.setFont(undefined, 'normal');
    doc.setFontSize(10);
    
    // Get top performers from the table (sort by score, take top 5)
    let topPerformers = [];
    if (table) {
      const tableRows = table.querySelectorAll('tr');
      for (let i = 1; i < tableRows.length; i++) {
        const cells = tableRows[i].querySelectorAll('td');
        if (cells.length >= 5) {
          const scoreText = cells[1].textContent.trim();
          const score = scoreText === 'No grades' ? 0 : parseFloat(scoreText.replace('%', ''));
          topPerformers.push({
            name: cells[0].textContent.trim(),
            score: scoreText
          });
        }
      }
      // Sort by score (descending) and take top 5
      topPerformers.sort((a, b) => {
        const scoreA = a.score === 'No grades' ? 0 : parseFloat(a.score.replace('%', ''));
        const scoreB = b.score === 'No grades' ? 0 : parseFloat(b.score.replace('%', ''));
        return scoreB - scoreA;
      });
      topPerformers = topPerformers.slice(0, 5);
    }
    
    if (topPerformers.length > 0) {
      topPerformers.forEach(performer => {
        if (y > pageHeight - 10) {
          doc.addPage();
          y = 20;
        }
        const performerInfo = `• ${performer.name} - ${performer.score}${performer.score !== 'No grades' ? '' : ''}`;
        const performerLines = doc.splitTextToSize(performerInfo, pageWidth - 20);
        performerLines.forEach(line => {
          if (y > pageHeight - 10) {
            doc.addPage();
            y = 20;
          }
          doc.text(line, 25, y);
          y += 5;
        });
      });
    } else {
      doc.text('No student performance data available.', 25, y);
      y += 6;
    }
    y += 10;
    
    // Detailed Student List Table
    if (y > pageHeight - 60) {
      doc.addPage();
      y = 20;
    }
    
    doc.setFont(undefined, 'bold');
    doc.setFontSize(12);
    doc.text('Detailed Student List', 20, y);
    y += 10;
    
    if (table) {
      // Table setup
      const headers = ['Student Name', 'Score', 'Views', 'Last Access', 'Status'];
      const colWidths = [50, 25, 20, 45, 25];
      const startX = 20;
      
      // Draw table header
      doc.setFont(undefined, 'bold');
      doc.setFontSize(9);
      
      // Header background
      doc.setFillColor(240, 240, 240);
      doc.rect(startX, y - 5, colWidths.reduce((a, b) => a + b, 0), 8, 'F');
      
      let xPos = startX;
      headers.forEach((header, index) => {
        doc.text(header, xPos + 2, y);
        xPos += colWidths[index];
      });
      y += 8;
      
      // Table rows
      doc.setFont(undefined, 'normal');
      const tableRows = table.querySelectorAll('tr');
      
      for (let i = 1; i < tableRows.length; i++) {
        const cells = tableRows[i].querySelectorAll('td');
        if (cells.length >= 5) {
          // Check if we need a new page
          if (y > pageHeight - 15) {
            doc.addPage();
            y = 20;
            
            // Redraw header on new page
            doc.setFont(undefined, 'bold');
            doc.setFillColor(240, 240, 240);
            doc.rect(startX, y - 5, colWidths.reduce((a, b) => a + b, 0), 8, 'F');
            
            xPos = startX;
            headers.forEach((header, index) => {
              doc.text(header, xPos + 2, y);
              xPos += colWidths[index];
            });
            y += 8;
            doc.setFont(undefined, 'normal');
          }
          
          // Draw row border
          doc.setDrawColor(200, 200, 200);
          doc.rect(startX, y - 5, colWidths.reduce((a, b) => a + b, 0), 8);
          
          // Add cell data
          xPos = startX;
          const rowData = [
            cells[0].textContent.trim(),
            cells[1].textContent.trim(),
            cells[2].textContent.trim(),
            cells[3].textContent.trim(),
            cells[4].textContent.trim()
          ];
          
          rowData.forEach((cellData, index) => {
            // Handle long text by truncating if necessary
            let displayText = cellData;
            if (index === 0 && cellData.length > 20) { // Student name
              displayText = cellData.substring(0, 18) + '...';
            } else if (index === 3 && cellData.length > 15) { // Last access
              displayText = cellData.substring(0, 13) + '...';
            }
            
            doc.text(displayText, xPos + 2, y);
            xPos += colWidths[index];
          });
          y += 8;
        }
      }
    }
    
    y += 10;
    
    // Recommendations
    if (y > pageHeight - 30) {
      doc.addPage();
      y = 20;
    }
    
    doc.setFont(undefined, 'bold');
    doc.setFontSize(12);
    doc.text('Recommendations', 20, y);
    y += 8;
    
    doc.setFont(undefined, 'normal');
    doc.setFontSize(10);
    
    // Extract recommendations from the report
    const recommendationsText = reportElement.textContent;
    const recommendations = [];
    
    if (atRiskStudents.length > 0) {
      recommendations.push(`Contact ${atRiskStudents.length} at-risk students who haven't accessed the course in over 7 days`);
    }
    
    const lowEngagementMatch = recommendationsText.match(/Follow up with (\d+) students showing low engagement/);
    if (lowEngagementMatch) {
      recommendations.push(`Follow up with ${lowEngagementMatch[1]} students showing low engagement (less than 50 page views)`);
    }
    
    const avgScoreMatch = recommendationsText.match(/Class average of ([\d.]+)% suggests/);
    if (avgScoreMatch && parseFloat(avgScoreMatch[1]) < 70) {
      recommendations.push(`Class average of ${avgScoreMatch[1]}% suggests need for additional support or review`);
    }
    
    if (recommendations.length === 0) {
      recommendations.push('Class is performing well! Continue current teaching strategies.');
    }
    
    recommendations.forEach(rec => {
      if (y > pageHeight - 10) {
        doc.addPage();
        y = 20;
      }
      const recLines = doc.splitTextToSize(`• ${rec}`, pageWidth - 20);
      recLines.forEach(line => {
        if (y > pageHeight - 10) {
          doc.addPage();
          y = 20;
        }
        doc.text(line, 25, y);
        y += 5;
      });
    });
    
    // Save the PDF
    doc.save(filename);
  };

  // Download CSV
  window.downloadCSV = function(courseId) {
    const csvData = document.getElementById('csv-data-' + courseId).textContent.trim();
    const courseName = document.querySelector(`#report-content-${courseId} h2`).textContent.replace('Performance Report - ', '');
    const date = new Date().toISOString().split('T')[0];
    const filename = `${courseName}_Performance_Report_${date}.csv`;
    
    const blob = new Blob([csvData], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    
    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob);
      link.setAttribute('href', url);
      link.setAttribute('download', filename);
      link.style.visibility = 'hidden';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
    } else {
      // Fallback for browsers that don't support download attribute
      const url = window.URL.createObjectURL(blob);
      window.open(url);
    }
  };

  });
</script>

