<% content_for :page_title, @page_title %>

<div class="consultation-summaries-page">
  <h1>Consultation Summaries</h1>
  <p>Debug info:</p>
  <ul>
    <li>Current user: <%= @current_user&.name %> (ID: <%= @current_user&.id %>)</li>
    <li>Is faculty: <%= @current_user&.teacher_enrollments&.active&.exists? %></li>
    <li>Page title: <%= @page_title %></li>
  </ul>

  <div id="consultation-summaries-container">
    <!-- React component will be mounted here -->
    <p>Container is ready for React component...</p>
  </div>

  <script>
    console.log('Page loaded');
    console.log('ENV data:', window.ENV?.CONSULTATION_SUMMARIES);
    console.log('Container found:', document.getElementById('consultation-summaries-container'));
  </script>
</div>
