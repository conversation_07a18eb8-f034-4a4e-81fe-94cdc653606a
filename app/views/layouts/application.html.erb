<%
# Copyright (C) 2011 - present Instructure, Inc.
#
# This file is part of Canvas.
#
# Canvas is free software: you can redistribute it and/or modify it under
# the terms of the GNU Affero General Public License as published by the Free
# Software Foundation, version 3 of the License.
#
# Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
# WARRANTY; without even the implied warranty of MERCHANT<PERSON>ILITY or FITNESS FOR
# A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
# details.
#
# You should have received a copy of the GNU Affero General Public License along
# with this program. If not, see <http://www.gnu.org/licenses/>.
%>
<%-
  css_bundle(:instructure_eportfolio) if @eportfolio_view === true
  css_bundle(:new_user_tutorials) if tutorials_enabled?
  js_bundle(:navigation_header) unless @headers == false
  if @domain_root_account&.feature_enabled?(:top_navigation_placement)
    js_env INIT_DRAWER_LAYOUT_MUTEX: "init-drawer-layout"
    js_bundle(:top_navigation_tools)
end
  load_blueprint_courses_ui
  @has_content_notices = load_content_notices
  provide :head, include_common_stylesheets
  set_badge_counts_for(@context, @current_user) if @set_badge_counts
  js_env notices: flash_notices()
  student_view_text = @context.is_a?(Course) && @context.horizon_course? ? t('View as Learner') : t('View as Student')
-%>
<%= render :partial => "layouts/head" %>
<%-
  left_side = nil
  left_side_custom = nil
  right_side = (yield :right_side).presence
  # Collapse the course menu according to user preference, unless a certain action
  # requests that it default to collapsed for that page
  @collapse_course_menu ||= @current_user&.reload&.collapse_course_nav?
  context_is_course_or_account = @context&.is_a?(Course) || @context&.is_a?(Account)
  @enhanced_rubrics_enabled = context_is_course_or_account ? @context.feature_enabled?(:enhanced_rubrics) : false
  if @collapse_course_menu
    subnav_menu_text = t('Show Navigation Menu')
    subnav_menu_text = t('Show Courses Navigation Menu') if active_path?('/courses')
    subnav_menu_text = t('Show Account Navigation Menu') if active_path?('/profile')
    subnav_menu_text = t('Show Admin Navigation Menu') if active_path?('/accounts')
    subnav_menu_text = t('Show Groups Navigation Menu') if active_path?('/groups')
  else
    subnav_menu_text = t('Hide Navigation Menu')
    subnav_menu_text = t('Hide Courses Navigation Menu') if active_path?('/courses')
    subnav_menu_text = t('Hide Account Navigation Menu') if active_path?('/profile')
    subnav_menu_text = t('Hide Admin Navigation Menu') if active_path?('/accounts')
    subnav_menu_text = t('Hide Groups Navigation Menu') if active_path?('/groups')
  end
  @collapse_global_nav = @current_user.try(:collapse_global_nav?)
  @body_class_no_headers = @headers == false
  @show_embedded_chat = embedded_chat_visible
  @show_fixed_bottom = (@fixed_warnings.present? || (@real_current_user && @real_current_user != @current_user)) && (!@body_class_no_headers || @outer_frame)
  body_classes << "no-headers" if @body_class_no_headers
  unless (body_classes.include? "no-headers") || (@show_left_side == false)
    left_side = nil
    skip_for_streaming :left_side, except: ["eportfolios/show", "eportfolio_categories/show", "eportfolio_entries/show"] do
      left_side = (yield :left_side).presence
    end
    @show_left_side ||= (section_tabs.length > 0)
  end
  body_classes << "with-left-side" if @show_left_side
  body_classes << "course-menu-expanded" if body_classes.include?("with-left-side") && !@collapse_course_menu
  #we dont want to render a right side unless there is actually content in it.
  body_classes << "with-right-side" if right_side and not right_side.strip.empty?
  body_classes << "padless-content" if @padless
  body_classes << "with-embedded-chat" if @show_embedded_chat
  body_classes << 'with-fixed-bottom' if @show_fixed_bottom
  body_classes << 'pages' if controller.js_env[:WIKI_PAGE].present?
  body_classes << get_active_tab
  body_classes << 'Underline-All-Links__enabled' if @current_user && @current_user.feature_enabled?(:underline_all_links)
  body_classes << 'is-masquerading-or-student-view' if @real_current_user && @real_current_user != @current_user
  body_classes << 'primary-nav-expanded' unless @collapse_global_nav
  body_classes << 'primary-nav-transitions' if @collapse_global_nav
  # We probably want to consider doing this everywhere, all the time, but when I did
  # for LS-1745, people complained a lot, so maybe not.
  # Putting this behind a feature flag because of the above comment.
  body_classes << 'full-width' if @domain_root_account.try(:feature_enabled?, :full_width_everywhere) || @domain_root_account.try(:feature_enabled?, :new_user_tutorial)
  body_classes << "context-#{@context.asset_string}" if @context
  body_classes << "responsive_student_grades_page" if !!@domain_root_account&.feature_enabled?(:responsive_student_grades_page)
-%>
<body class="<%= (body_classes).uniq.join(" ") %>">
<%if @current_user && @real_current_user && @real_current_user != @current_user %>
  <div role="alert" class="screenreader-only">
    <% if @current_user.fake_student? %>
        <%= t("You are currently logged into Student View") %>
      <% else %>
        <%= t("You are currently acting as %{user_name}", :user_name => @current_user.short_name) %>
    <% end %>
  </div>
<% end %>
<%# Flash messages must be outside of #application or they won't work in screenreaders with modals open. %>
<%= render :partial => 'shared/static_notices' %>
<%= render :partial => 'shared/flash_notices' %>
<%if @domain_root_account&.feature_enabled?(:top_navigation_placement) %>
  <div id="drawer-layout-mount-point"></div>
<% end %>

<% unless @headers == false %>
<header class="rsu-header">
  <div class="rsu-header-content">
    <div class="rsu-logo">
      <%
        # Determine dashboard info like sidebar does
        if k5_user?
          dashboard_title = t('Home')
        else
          dashboard_title = t('Dashboard')
        end
      %>
      
      <a href="<%= dashboard_url %>" class="rsu-logo-link" title="<%= dashboard_title %>">
        <span class="screenreader-only"><%= dashboard_title %></span>
        <% 
          # Try to get the navigation logo from brand config
          nav_logo = @domain_root_account&.brand_config&.[]('ic-brand-header-image') || 
                     @domain_root_account&.brand_config&.[]('ic-brand-mobile-global-nav-logo')
        %>
        
        <% if nav_logo.present? %>
          <img src="<%= nav_logo %>" alt="Romblon State University Logo - <%= dashboard_title %>" />
        <% elsif @domain_root_account&.brand_config&.[]('ic-brand-header-image') %>
          <img src="<%= @domain_root_account.brand_config['ic-brand-header-image'] %>" alt="Romblon State University Logo - <%= dashboard_title %>" />
        <% elsif File.exist?(Rails.root.join('public', 'images', 'rsu', 'university-logo.png')) %>
          <img src="/images/rsu/university-logo.png" alt="Romblon State University Logo - <%= dashboard_title %>" />
        <% elsif File.exist?(Rails.root.join('public', 'images', 'rsu-logo.png')) %>
          <img src="/images/rsu-logo.png" alt="Romblon State University Logo - <%= dashboard_title %>" />
        <% else %>
          <div style="width: 60px; height: 60px; background: white; border-radius: 50%; display: flex; align-items: center; justify-content: center; color: #2d5f3f; font-weight: bold;">RSU</div>
        <% end %>
      </a>
    </div>
    <div class="rsu-text">
      <h1 class="rsu-university-name">Romblon State University</h1>
      <p class="rsu-system-name">Learning Management System</p>
    </div>
    
    <div class="rsu-actions">
      <% if @current_user %>
        <%= link_to logout_path, method: :delete, class: "rsu-signout-btn", 
            title: t('Sign Out'),
            'aria-label': t('Sign Out') do %>
          <span class="rsu-signout-text"><%= t('Sign Out') %></span>
        <% end %>
      <% end %>
    </div>
  </div>
</header>
<% end %>

<div id="application" class="ic-app">
  <%= render(:partial => 'shared/new_nav_header') unless @headers == false %>

  <div id="instructure_ajax_error_box">
    <div style="text-align: <%= direction('right') %>; background-color: #fff;"><a href="#" class="close_instructure_ajax_error_box_link"><%= t('links.close', 'Close') %></a></div>
    <iframe id="instructure_ajax_error_result" src="about:blank" style="border: 0;" title="<%= t('Error') %>"></iframe>
  </div>

  <div id="wrapper" class="ic-Layout-wrapper">
    <% if @instui_topnav %>
      <div class="instui-topnav-container">
        <div id="react-instui-topnav"></div>
      </div>
    <% else %>
      <% if crumbs.length > 1 %>
        <div class="ic-app-nav-toggle-and-crumbs no-print">
          <% if @show_left_side %>
            <button type="button" id="courseMenuToggle" class="Button Button--link ic-app-course-nav-toggle" aria-live="polite" aria-label="<%= subnav_menu_text %>">
              <i class="icon-hamburger" aria-hidden="true"></i>
            </button>
          <% end %>

          <div class="ic-app-crumbs <%= 'ic-app-crumbs-enhanced-rubrics' if @enhanced_rubrics_enabled %>">
            <% if @context&.is_a?(Course) && @context.elementary_subject_course? %>
              <%= link_to course_path(id: @context.id), :class => "btn k5-back-to-subject", :id => "back_to_subject" do %>
                <i class="icon-arrow-open-left"></i> <%= t('Back to Subject') %>
              <% end %>
            <% else %>
              <%= render_crumbs %>
            <% end %>
          </div>

          <% if @context&.is_a?(Course) && @context.elementary_subject_course? %>
            <span class="k5-heading-course-name"><%= @context.nickname_for(@current_user) %></span>
          <% end %>

          <div class="right-of-crumbs">
            <% if tutorials_enabled? %>
              <div class="TutorialToggleHolder"></div>
            <% end %>
            <%if @domain_root_account&.feature_enabled?(:top_navigation_placement) %>
              <div id="top-nav-tools-mount-point"></div>
            <% end %>
            <% if show_immersive_reader? %>
              <div id="immersive_reader_mount_point"></div>
            <% end %>
            <% if show_student_view_button? %>
              <%= link_to course_student_view_path(course_id: @context, redirect_to_referer: 1), :class => "btn btn-top-nav", :id => "easy_student_view", :method => :post, :"aria-label" => student_view_text do %>
                <i class="icon-student-view"></i> <%= student_view_text %>
              <% end %>
            <% end %>
            <% if (@context&.is_a?(Course) || @context&.is_a?(Assignment)) && @context_enrollment&.observer? %>
              <div id='observer-picker-mountpoint' style="margin: 3px;"></div>
            <% end %>
          </div>

        </div>
      <% end %>
    <% end %>
    <div id="main" class="ic-Layout-columns">
      <% if !@body_class_no_headers %>
        <div class="ic-Layout-watermark"></div>
      <% end %>
      <% if @show_left_side %>
        <% if @no_left_side_list_view
            list_view_class = ''
          else
            list_view_class = 'list-view'
          end
        %>
        <div id="left-side"
          class="ic-app-course-menu ic-sticky-on <%= list_view_class %>"
          style="display: <%= @collapse_course_menu ? "none" : "block" %>"
          >
          <div id="sticky-container" class="ic-sticky-frame">
          <% if left_side %>
            <%= left_side %>
          <% else %>
            <% if @context && @context.is_a?(Group) && can_do(@context, @current_user, :manage) && @context.group_category %>
              <span id="group-switch-mount-point"></span>
            <% end %>
            <% if @context && @context.respond_to?(:enrollment_term) && !@context.enrollment_term.default_term? %>
              <span id="section-tabs-header-subtitle" class="ellipsis"><%= @context.enrollment_term.name %></span>
            <% end %>
            <%= section_tabs %>
          <% end %>
          </div>
        </div>
      <% end %>
      <div id="not_right_side" class="ic-app-main-content">
        <div id="content-wrapper" class="ic-Layout-contentWrapper">
          <% if @syllabus_reading_required %>
            <%= render partial: 'shared/syllabus_reading_required' %>
          <% else %>
            <% if should_show_migration_limitation_message %>
              <% js_bundle :quiz_migration_alerts %>
              <div class="ic-notification ic-notification--info quiz_migration_notification">
                <div class="ic-notification__icon" role="presentation">
                <i class="icon-info"></i>
                <span class="screenreader-only">
                  <%= accessible_message_icon_text('information') %>
                </span>
              </div>
              <div class="ic-notification__content">
                <div class="ic-notification__message notification_message" style="margin-bottom:1rem;">
                  <%= t 'Your Classic Quizzes have been migrated to New Quizzes! ' %><br />
                  <% if show_migration_text_no_question_warning? %>
                    <%= t 'Please note:' %>
                    <ul>
                      <li><%= t 'Text No Question has moved to a Stimulus; please add a question so it can display within the quiz.' %></li>
                    </ul>
                    <%= t 'We apologize for the inconvenience and thank you for your patience as we continue to improve the migration experience!' %>
                  <% end %>
                </div>
                <div class="ic-notification__actions">
                  <a href="#"
                    rel="<%= api_v1_course_dismiss_migration_limitation_msg_url(@context.id) %>"
                    class="close_migration_notification_link Button Button--info"
                    role="button"
                  >
                    <%= t('Close') %>
                  </a>
                </div>
              </div>
            </div>
          <% end %>
          <%= render :partial => 'shared/content_notices' if @has_content_notices && @show_left_side %>
          <%= render :partial => 'layouts/course_concluded_notice' if @context.is_a?(Course) %>

          <div id="content" class="ic-Layout-contentMain<%= ' course-concluded-content' if @context.is_a?(Course) && @context.concluded? && !@context.course_is_restored_for_ui? %>" role="main" aria-label="<%= t("Global Content") %>">
            <%= yield %>
          </div>
        </div>
        <div id="right-side-wrapper" class="ic-app-main-content__secondary<%= ' course-concluded-content' if @context.is_a?(Course) && @context.concluded? && !@context.course_is_restored_for_ui? %>">
          <aside id="right-side" role="complementary">
            <%= right_side %>
          </aside>
        </div>
          <% end %>
        </div>
      </div>
    </div>
    <% if @show_footer %>
      <%= render :partial => 'shared/canvas_footer' %>
    <% end %>
  </div>

  <% if @show_embedded_chat %>
    <%= render :partial => 'shared/embedded_chat' %>
  <% end %>

  <% if @show_fixed_bottom %>
    <%= render :partial => 'layouts/fixed_bottom' %>
  <% end %>

  <% if (wizard = (yield :wizard_box).presence) %>
    <div id="wizard_box" tabindex="-1">
      <div class="wizard_content">
        <div class="links">
          <a href="#" class="close_wizard_link"><i class="icon-x"></i><span class="screenreader-only"><%= t('links.close', 'Close') %></span></a>
        </div>
        <%= wizard %>
      </div>
    </div>
  <% end %>
  <% if (keyboard_navigation = (yield :keyboard_navigation).presence) %>
    <div id="keyboard_navigation">
      <%= keyboard_navigation %>
      <div class='hidden-readable' tabindex='0'>
        <%= t('keyboard_navigation.close', 'Press comma to close this dialog') %>
      </div>
    </div>
  <% end %>
    <div style="display:none;"><!-- Everything inside of this should always stay hidden -->
      <% if @context && session && temp_type = session["role_#{@context.asset_string}"] %>
        <span id="switched_role_type" class="<%= @context.asset_string %>" data-role="<%= temp_type %>"><%= Enrollment.readable_type(temp_type) %></span>
      <% end %>
      <% if @page_view %>
        <div id="page_view_id"><%= @page_view.id %></div>
      <% end %>
      <% if equella_enabled? %>
        <a id="equella_endpoint_url" href="<%= @equella_settings[:endpoint] %>">&nbsp;</a>
        <a id="equella_callback_url" href="<%= external_content_success_url('equella') %>">&nbsp;</a>
        <a id="equella_cancel_url" href="<%= external_content_cancel_url('equella') %>">&nbsp;</a>
        <a id="equella_action" href="<%= @equella_settings[:default_action] %>">&nbsp;</a>
        <% if @equella_settings[:teaser] %>
          <div id="equella_teaser"><%= @equella_settings[:teaser] %></div>
        <% end %>
      <% end %>
    </div>
  <div id='aria_alerts' class='hide-text affix' role="alert" aria-live="assertive"></div>
  <div id='StudentTray__Container'></div>
  <% if tutorials_enabled? %>
    <div class="NewUserTutorialTray__Container"></div>
  <% end %>
  <div id="react-router-portals"></div>
  <%= render :partial => 'layouts/foot', :locals => { :include_common_bundle => true } %>
</div> 

<script src="/javascripts/auto_logout.js"></script>
</body>
</html>