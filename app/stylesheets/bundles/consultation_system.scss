// Consultation System Styles

.consultation-system {
  .page-header {
    background: #f8f9fa;
    padding: 1.5rem;
    border-bottom: 1px solid #dee2e6;
    margin-bottom: 2rem;

    h1 {
      margin: 0;
      color: #2c3e50;
      font-size: 1.75rem;
      font-weight: 600;
    }

    .breadcrumb {
      margin: 0.5rem 0 0 0;
      background: transparent;
      padding: 0;
    }
  }

  .stats-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;

    .stat-card {
      background: white;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 1.5rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .stat-number {
        font-size: 2rem;
        font-weight: bold;
        color: #007bff;
        margin-bottom: 0.5rem;
      }

      .stat-label {
        color: #6c757d;
        font-size: 0.9rem;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }

      &.pending {
        border-left: 4px solid #ffc107;
        .stat-number { color: #ffc107; }
      }

      &.approved {
        border-left: 4px solid #28a745;
        .stat-number { color: #28a745; }
      }

      &.completed {
        border-left: 4px solid #17a2b8;
        .stat-number { color: #17a2b8; }
      }
    }
  }

  .consultation-form {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .form-section {
      margin-bottom: 2rem;

      h3 {
        color: #495057;
        margin-bottom: 1rem;
        font-size: 1.1rem;
        font-weight: 600;
      }
    }

    .form-group {
      margin-bottom: 1.5rem;

      label {
        display: block;
        margin-bottom: 0.5rem;
        font-weight: 500;
        color: #495057;
      }

      .required {
        color: #dc3545;
      }

      input, select, textarea {
        width: 100%;
        padding: 0.75rem;
        border: 1px solid #ced4da;
        border-radius: 4px;
        font-size: 1rem;
        transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;

        &:focus {
          border-color: #007bff;
          outline: 0;
          box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        &.error {
          border-color: #dc3545;
          box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.25);
        }
      }

      textarea {
        min-height: 100px;
        resize: vertical;
      }

      .help-text {
        font-size: 0.875rem;
        color: #6c757d;
        margin-top: 0.25rem;
      }

      .error-message {
        color: #dc3545;
        font-size: 0.875rem;
        margin-top: 0.25rem;
      }
    }

    .form-actions {
      display: flex;
      gap: 1rem;
      justify-content: flex-end;
      margin-top: 2rem;
      padding-top: 1.5rem;
      border-top: 1px solid #e9ecef;
    }
  }

  .time-slot-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;

    .time-slot-card {
      background: white;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 1.5rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .slot-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 1rem;

        .day-name {
          font-weight: 600;
          color: #495057;
          font-size: 1.1rem;
        }

        .slot-status {
          padding: 0.25rem 0.75rem;
          border-radius: 12px;
          font-size: 0.75rem;
          font-weight: 500;
          text-transform: uppercase;

          &.available {
            background: #d4edda;
            color: #155724;
          }

          &.unavailable {
            background: #f8d7da;
            color: #721c24;
          }
        }
      }

      .slot-time {
        color: #007bff;
        font-weight: 500;
        margin-bottom: 0.5rem;
      }

      .slot-type {
        color: #6c757d;
        font-size: 0.9rem;
        margin-bottom: 1rem;
      }

      .slot-notes {
        color: #495057;
        font-size: 0.9rem;
        margin-bottom: 1rem;
        font-style: italic;
      }

      .slot-actions {
        display: flex;
        gap: 0.5rem;
        justify-content: flex-end;
      }

      .pending-requests {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        padding: 0.5rem;
        margin-top: 1rem;
        font-size: 0.875rem;
        color: #856404;
      }
    }
  }

  .consultation-list {
    .consultation-item {
      background: white;
      border: 1px solid #e9ecef;
      border-radius: 8px;
      padding: 1.5rem;
      margin-bottom: 1rem;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 1rem;

        .student-info {
          h4 {
            margin: 0 0 0.25rem 0;
            color: #495057;
          }

          .student-id {
            color: #6c757d;
            font-size: 0.9rem;
          }
        }

        .status-badge {
          padding: 0.5rem 1rem;
          border-radius: 20px;
          font-size: 0.875rem;
          font-weight: 500;
          text-transform: uppercase;

          &.pending {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
          }

          &.approved {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
          }

          &.declined {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
          }

          &.completed {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
          }
        }
      }

      .consultation-details {
        margin-bottom: 1rem;

        .detail-row {
          display: flex;
          margin-bottom: 0.5rem;

          .label {
            font-weight: 500;
            color: #495057;
            min-width: 120px;
          }

          .value {
            color: #6c757d;
          }
        }

        .concern-type {
          display: inline-block;
          background: #e9ecef;
          color: #495057;
          padding: 0.25rem 0.75rem;
          border-radius: 12px;
          font-size: 0.875rem;
          margin-top: 0.5rem;
        }
      }

      .description {
        background: #f8f9fa;
        border-left: 4px solid #007bff;
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 0 4px 4px 0;
        font-style: italic;
      }

      .faculty-comment {
        background: #fff3cd;
        border-left: 4px solid #ffc107;
        padding: 1rem;
        margin: 1rem 0;
        border-radius: 0 4px 4px 0;

        .comment-label {
          font-weight: 500;
          color: #856404;
          margin-bottom: 0.5rem;
        }
      }

      .item-actions {
        display: flex;
        gap: 0.75rem;
        justify-content: flex-end;
        margin-top: 1.5rem;
        padding-top: 1rem;
        border-top: 1px solid #e9ecef;
      }
    }
  }

  .filters-section {
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);

    .filters-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
      gap: 1rem;
      align-items: end;
    }

    .filter-actions {
      display: flex;
      gap: 0.5rem;
    }
  }

  .empty-state {
    text-align: center;
    padding: 3rem;
    color: #6c757d;

    .empty-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
      opacity: 0.5;
    }

    h3 {
      margin-bottom: 0.5rem;
      color: #495057;
    }

    p {
      margin-bottom: 1.5rem;
    }
  }

  .loading-spinner {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;

    .spinner {
      width: 2rem;
      height: 2rem;
      border: 3px solid #f3f3f3;
      border-top: 3px solid #007bff;
      border-radius: 50%;
      animation: spin 1s linear infinite;
    }
  }

  @keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
  }

  // Faculty Time Slot Calendar Styles
  .faculty-calendar {
    .calendar-navigation {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 1.5rem;
      padding: 1rem;
      background: #f8f9fa;
      border-radius: 8px;
      border: 1px solid #e9ecef;
    }

    .calendar-grid {
      border: 1px solid #e9ecef;
      border-radius: 8px;
      overflow: hidden;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
      user-select: none; // Prevent text selection during drag operations
    }

    .calendar-header {
      background: #495057;
      color: white;
      display: flex;
      align-items: center;
      border-bottom: 1px solid #e9ecef;

      .time-column {
        width: 80px;
        padding: 0.75rem;
        text-align: center;
        font-weight: 600;
        border-right: 1px solid #6c757d;
      }

      .day-column {
        flex: 1;
        padding: 0.75rem;
        text-align: center;
        font-weight: 600;
        border-right: 1px solid #6c757d;

        &:last-child {
          border-right: none;
        }
      }
    }

    .calendar-row {
      display: flex;
      align-items: stretch;
      min-height: 40px;
      border-bottom: 1px solid #e9ecef;

      &:last-child {
        border-bottom: none;
      }

      .time-cell {
        width: 80px;
        padding: 0.5rem;
        text-align: center;
        background: #f8f9fa;
        border-right: 1px solid #e9ecef;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 0.875rem;
        color: #6c757d;
      }

      .slot-cell {
        flex: 1;
        padding: 1px;
        border-right: 1px solid #e9ecef;
        position: relative;
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 34px;

        &:last-child {
          border-right: none;
        }

        .slot-button {
          width: 100%;
          height: 32px;
          border: none;
          border-radius: 4px;
          font-size: 0.75rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          position: relative;
          display: flex;
          align-items: center;
          justify-content: center;

          &.available {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;

            &:hover {
              background: #c3e6cb;
              transform: translateY(-1px);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }

            // Enhanced styling for multi-slot buttons
            &[style*="position: absolute"] {
              border: 2px solid #c3e6cb;
              box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);

              &:hover {
                background: #c3e6cb;
                border-color: #b3d7cc;
                transform: none; // Disable transform for absolute positioned elements
                box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
              }
            }
          }

          &.approved {
            background: #cce7ff;
            color: #004085;
            border: 1px solid #b3d7ff;

            &:hover {
              background: #b3d7ff;
              transform: translateY(-1px);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
          }

          &.declined {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;

            &:hover {
              background: #f5c6cb;
              transform: translateY(-1px);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
          }

          &.pending {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;

            &:hover {
              background: #ffeaa7;
              transform: translateY(-1px);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
          }

          &.declined {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f1aeb5;

            &:hover {
              background: #f1aeb5;
              transform: translateY(-1px);
              box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            }
          }

          &.unavailable {
            background: #e9ecef;
            color: #6c757d;
            border: 1px solid #ced4da;
            cursor: not-allowed;

            &:hover {
              background: #e9ecef;
              transform: none;
              box-shadow: none;
            }
          }

          &.past {
            background: #f8f9fa;
            color: #adb5bd;
            border: 1px solid #dee2e6;
            cursor: not-allowed;
            opacity: 0.6;

            &:hover {
              background: #f8f9fa;
              transform: none;
              box-shadow: none;
            }
          }

          // Past slots with specific statuses - maintain original colors but dimmed
          &.available.past {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
            cursor: not-allowed;
            opacity: 0.5;

            &:hover {
              background: #d4edda;
              transform: none;
              box-shadow: none;
            }
          }

          &.approved.past {
            background: #cce7ff;
            color: #004085;
            border: 1px solid #b3d7ff;
            cursor: not-allowed;
            opacity: 0.5;

            &:hover {
              background: #cce7ff;
              transform: none;
              box-shadow: none;
            }
          }

          &.declined.past {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f1aeb5;
            cursor: not-allowed;
            opacity: 0.5;

            &:hover {
              background: #f8d7da;
              transform: none;
              box-shadow: none;
            }
          }

          &.pending.past {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
            cursor: not-allowed;
            opacity: 0.5;

            &:hover {
              background: #fff3cd;
              transform: none;
              box-shadow: none;
            }
          }
        }



        .slot-actions {
          position: absolute;
          top: 0;
          right: 0;
          display: flex;
          gap: 2px;
          opacity: 0;
          transition: opacity 0.2s ease;
          z-index: 3;

          .action-button {
            width: 16px;
            height: 16px;
            border: none;
            border-radius: 2px;
            background: rgba(0, 0, 0, 0.7);
            color: white;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 0.625rem;

            &:hover {
              background: rgba(0, 0, 0, 0.9);
            }
          }
        }

        &:hover .slot-actions {
          opacity: 1;
        }

        .empty-slot {
          width: 100%;
          height: 32px;
          min-height: 32px;
          background: #f8f9fa;
          border: 1px dashed #ced4da;
          border-radius: 4px;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 0.75rem;
          color: #adb5bd;
          cursor: default;
          user-select: none; // Prevent text selection during drag

          &.clickable {
            cursor: pointer;
            transition: all 0.2s ease;

            &:hover {
              background: #e9ecef;
              border-color: #007bff;
              color: #007bff;
              transform: translateY(-1px);
              box-shadow: 0 2px 4px rgba(0, 123, 255, 0.2);
            }

            &:active {
              transform: translateY(0);
              box-shadow: 0 1px 2px rgba(0, 123, 255, 0.2);
            }
          }

          &.selecting {
            background: #cce7ff;
            border: 2px solid #007bff;
            color: #004085;
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0, 123, 255, 0.3);

            &:hover {
              background: #b3d7ff;
              border-color: #0056b3;
            }
          }

          &.past {
            opacity: 0.5;
            cursor: not-allowed;
          }
        }
      }
    }
  }

  // Responsive design
  @media (max-width: 768px) {
    .stats-cards {
      grid-template-columns: 1fr;
    }

    .time-slot-grid {
      grid-template-columns: 1fr;
    }

    .consultation-item {
      .item-header {
        flex-direction: column;
        gap: 1rem;
      }

      .item-actions {
        flex-direction: column;
      }
    }

    .filters-section .filters-grid {
      grid-template-columns: 1fr;
    }

    .faculty-calendar {
      .calendar-navigation {
        flex-direction: column;
        gap: 1rem;
      }

      .calendar-row {
        .time-cell {
          width: 60px;
          font-size: 0.75rem;
        }

        .slot-cell .slot-button {
          font-size: 0.625rem;
          height: 28px;
        }
      }
    }
  }
}

// Button styles
.btn {
  display: inline-block;
  padding: 0.5rem 1rem;
  border: 1px solid transparent;
  border-radius: 4px;
  font-size: 0.875rem;
  font-weight: 500;
  text-align: center;
  text-decoration: none;
  cursor: pointer;
  transition: all 0.15s ease-in-out;

  &.btn-primary {
    background: #007bff;
    color: white;
    border-color: #007bff;

    &:hover {
      background: #0056b3;
      border-color: #0056b3;
    }
  }

  &.btn-success {
    background: #28a745;
    color: white;
    border-color: #28a745;

    &:hover {
      background: #1e7e34;
      border-color: #1e7e34;
    }
  }

  &.btn-warning {
    background: #ffc107;
    color: #212529;
    border-color: #ffc107;

    &:hover {
      background: #e0a800;
      border-color: #e0a800;
    }
  }

  &.btn-danger {
    background: #dc3545;
    color: white;
    border-color: #dc3545;

    &:hover {
      background: #c82333;
      border-color: #c82333;
    }
  }

  &.btn-secondary {
    background: #6c757d;
    color: white;
    border-color: #6c757d;

    &:hover {
      background: #545b62;
      border-color: #545b62;
    }
  }

  &.btn-outline-primary {
    color: #007bff;
    border-color: #007bff;

    &:hover {
      background: #007bff;
      color: white;
    }
  }

  &.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.8rem;
  }

  &.btn-lg {
    padding: 0.75rem 1.5rem;
    font-size: 1rem;
  }

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }
}

// Modal specific styles
.consultation-modal {
  .modal-body {
    max-height: 70vh;
    overflow-y: auto;
  }

  .consultation-request-card {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1rem;
    background: #f8f9fa;

    &:last-child {
      margin-bottom: 0;
    }

    .request-header {
      display: flex;
      justify-content: space-between;
      align-items: flex-start;
      margin-bottom: 0.75rem;

      .student-info {
        .student-name {
          font-weight: 600;
          color: #495057;
          margin-bottom: 0.25rem;
        }

        .student-details {
          font-size: 0.875rem;
          color: #6c757d;
        }
      }

      .status-badge {
        padding: 0.25rem 0.75rem;
        border-radius: 12px;
        font-size: 0.75rem;
        font-weight: 500;
        text-transform: uppercase;

        &.pending {
          background: #fff3cd;
          color: #856404;
          border: 1px solid #ffeaa7;
        }

        &.declined {
          background: #f8d7da;
          color: #721c24;
          border: 1px solid #f5c6cb;
        }

        &.approved {
          background: #d4edda;
          color: #155724;
          border: 1px solid #c3e6cb;
        }
      }
    }

    .request-details {
      .detail-item {
        margin-bottom: 0.5rem;
        font-size: 0.875rem;

        .label {
          font-weight: 500;
          color: #495057;
          display: inline-block;
          min-width: 100px;
        }

        .value {
          color: #6c757d;
        }
      }

      .description {
        background: white;
        border: 1px solid #e9ecef;
        border-radius: 4px;
        padding: 0.75rem;
        margin-top: 0.75rem;
        font-size: 0.875rem;
        line-height: 1.4;
      }

      .faculty-comment {
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 4px;
        padding: 0.75rem;
        margin-top: 0.75rem;
        font-size: 0.875rem;
        line-height: 1.4;

        .comment-label {
          font-weight: 500;
          color: #856404;
          margin-bottom: 0.25rem;
          display: block;
        }
      }
    }

    .request-actions {
      margin-top: 1rem;
      padding-top: 0.75rem;
      border-top: 1px solid #e9ecef;
      display: flex;
      justify-content: flex-end;
    }
  }

  .time-slot-info {
    background: #e7f3ff;
    border: 1px solid #b3d7ff;
    border-radius: 8px;
    padding: 1rem;
    margin-bottom: 1.5rem;

    .slot-title {
      font-weight: 600;
      color: #004085;
      margin-bottom: 0.5rem;
      display: flex;
      align-items: center;
      gap: 0.5rem;
    }

    .slot-details {
      font-size: 0.875rem;
      color: #004085;
    }
  }

  .empty-requests {
    text-align: center;
    padding: 2rem;
    color: #6c757d;

    .empty-icon {
      font-size: 2rem;
      margin-bottom: 1rem;
      opacity: 0.5;
    }

    .empty-message {
      font-size: 0.875rem;
    }
  }

  .modal-loading {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 2rem;

    .loading-text {
      margin-left: 0.75rem;
      color: #6c757d;
    }
  }
}

// Time slot modal specific styles
.time-slot-modal {
  .form-section {
    margin-bottom: 1.5rem;

    .section-title {
      font-weight: 600;
      color: #495057;
      margin-bottom: 1rem;
      font-size: 1rem;
    }
  }

  .form-row {
    display: flex;
    gap: 1rem;
    margin-bottom: 1rem;

    .form-field {
      flex: 1;
    }
  }

  .checkbox-field {
    margin: 1rem 0;

    label {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      font-weight: 500;
      color: #495057;
      cursor: pointer;
    }
  }

  .form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    margin-top: 1.5rem;
    padding-top: 1rem;
    border-top: 1px solid #e9ecef;
  }
}

// Responsive modal styles
@media (max-width: 768px) {
  .consultation-modal {
    .consultation-request-card {
      .request-header {
        flex-direction: column;
        gap: 0.75rem;
      }

      .request-actions {
        justify-content: stretch;

        .btn {
          flex: 1;
        }
      }
    }
  }

  .time-slot-modal {
    .form-row {
      flex-direction: column;
      gap: 0.75rem;
    }

    .form-actions {
      flex-direction: column;

      .btn {
        width: 100%;
      }
    }
  }
}
