# frozen_string_literal: true

namespace :consultation do
  namespace :calendar do
    desc "Sync all approved consultations to calendar events"
    task sync_all: :environment do
      puts "Starting consultation calendar sync..."
      
      start_time = Time.current
      synced_count = 0
      error_count = 0
      
      ConsultationRequest.approved.includes(:faculty, :student).find_each do |consultation|
        begin
          event = ConsultationCalendarService.create_calendar_event_for_consultation(consultation)
          if event
            synced_count += 1
            puts "✓ Synced consultation #{consultation.id} -> calendar event #{event.id}"
          else
            puts "- Consultation #{consultation.id} already has calendar event"
          end
        rescue => e
          error_count += 1
          puts "✗ Failed to sync consultation #{consultation.id}: #{e.message}"
        end
      end
      
      end_time = Time.current
      duration = (end_time - start_time).round(2)
      
      puts "\nSync completed in #{duration} seconds"
      puts "Synced: #{synced_count} consultations"
      puts "Errors: #{error_count} consultations"
    end

    desc "Clean up orphaned consultation calendar events"
    task cleanup: :environment do
      puts "Starting consultation calendar cleanup..."
      
      start_time = Time.current
      cleaned_count = 0
      
      # Find calendar events that reference consultation requests
      consultation_events = CalendarEvent.active
                                        .where("title LIKE ? OR description LIKE ?", "%Consultation:%", "%consultation_request_id:%")

      consultation_events.find_each do |event|
        consultation_id = ConsultationCalendarService.extract_consultation_id_from_event(event)
        next unless consultation_id

        consultation = ConsultationRequest.find_by(id: consultation_id)
        
        # If consultation doesn't exist or is not approved, mark event as deleted
        if consultation.nil?
          event.update!(workflow_state: 'deleted')
          cleaned_count += 1
          puts "✓ Cleaned up event #{event.id} - consultation #{consultation_id} not found"
        elsif !consultation.approved?
          event.update!(workflow_state: 'deleted')
          cleaned_count += 1
          puts "✓ Cleaned up event #{event.id} - consultation #{consultation_id} not approved (status: #{consultation.status})"
        end
      end
      
      end_time = Time.current
      duration = (end_time - start_time).round(2)
      
      puts "\nCleanup completed in #{duration} seconds"
      puts "Cleaned up: #{cleaned_count} orphaned events"
    end

    desc "Update all consultation calendar events with latest information"
    task update_all: :environment do
      puts "Starting consultation calendar update..."
      
      start_time = Time.current
      updated_count = 0
      error_count = 0
      
      ConsultationRequest.where(status: ['approved', 'completed']).includes(:faculty, :student).find_each do |consultation|
        begin
          ConsultationCalendarService.update_calendar_event_for_consultation(consultation)
          updated_count += 1
          puts "✓ Updated calendar event for consultation #{consultation.id}"
        rescue => e
          error_count += 1
          puts "✗ Failed to update consultation #{consultation.id}: #{e.message}"
        end
      end
      
      end_time = Time.current
      duration = (end_time - start_time).round(2)
      
      puts "\nUpdate completed in #{duration} seconds"
      puts "Updated: #{updated_count} consultations"
      puts "Errors: #{error_count} consultations"
    end

    desc "Show consultation calendar statistics"
    task stats: :environment do
      puts "Consultation Calendar Statistics"
      puts "=" * 40
      
      total_consultations = ConsultationRequest.count
      approved_consultations = ConsultationRequest.approved.count
      completed_consultations = ConsultationRequest.completed.count
      
      consultation_events = CalendarEvent.active
                                        .where("title LIKE ? OR description LIKE ?", "%Consultation:%", "%consultation_request_id:%")
                                        .count
      
      puts "Total consultation requests: #{total_consultations}"
      puts "Approved consultations: #{approved_consultations}"
      puts "Completed consultations: #{completed_consultations}"
      puts "Calendar events for consultations: #{consultation_events}"
      puts ""
      
      # Check for missing calendar events
      missing_events = 0
      ConsultationRequest.approved.find_each do |consultation|
        event = ConsultationCalendarService.send(:find_existing_calendar_event, consultation)
        missing_events += 1 unless event
      end
      
      puts "Approved consultations missing calendar events: #{missing_events}"
      
      if missing_events > 0
        puts ""
        puts "Run 'rake consultation:calendar:sync_all' to create missing calendar events"
      end
    end

    desc "Validate consultation calendar integration"
    task validate: :environment do
      puts "Validating consultation calendar integration..."
      puts "=" * 50
      
      errors = []
      warnings = []
      
      # Check for consultations without calendar events
      ConsultationRequest.approved.includes(:faculty).find_each do |consultation|
        event = ConsultationCalendarService.send(:find_existing_calendar_event, consultation)
        unless event
          errors << "Consultation #{consultation.id} (#{consultation.student_name}) is approved but has no calendar event"
        end
      end
      
      # Check for calendar events without valid consultations
      consultation_events = CalendarEvent.active
                                        .where("title LIKE ? OR description LIKE ?", "%Consultation:%", "%consultation_request_id:%")
      
      consultation_events.find_each do |event|
        consultation_id = ConsultationCalendarService.extract_consultation_id_from_event(event)
        if consultation_id
          consultation = ConsultationRequest.find_by(id: consultation_id)
          unless consultation&.approved?
            warnings << "Calendar event #{event.id} references consultation #{consultation_id} which is not approved"
          end
        else
          warnings << "Calendar event #{event.id} appears to be a consultation but has no consultation_request_id"
        end
      end
      
      # Check for time conflicts
      ConsultationRequest.approved.includes(:faculty).find_each do |consultation|
        start_time = consultation.preferred_datetime
        end_time = start_time + 30.minutes
        conflicts = ConsultationCalendarService.check_for_conflicts(consultation.faculty, start_time, end_time)
        
        if conflicts[:has_conflicts]
          warnings << "Consultation #{consultation.id} has calendar conflicts at #{start_time}"
        end
      end
      
      puts "Validation Results:"
      puts "Errors: #{errors.count}"
      puts "Warnings: #{warnings.count}"
      puts ""
      
      if errors.any?
        puts "ERRORS:"
        errors.each { |error| puts "  ✗ #{error}" }
        puts ""
      end
      
      if warnings.any?
        puts "WARNINGS:"
        warnings.each { |warning| puts "  ⚠ #{warning}" }
        puts ""
      end
      
      if errors.empty? && warnings.empty?
        puts "✓ All validations passed!"
      end
    end

    desc "Run all consultation calendar maintenance tasks"
    task maintenance: [:cleanup, :sync_all, :validate] do
      puts "\nAll consultation calendar maintenance tasks completed!"
    end
  end
end
