document.addEventListener("DOMContentLoaded", function () {
  if (window.location.pathname.includes("/login")) {
    return; // Exit the script if the user is on a login-related page
  }

  let warningTimeout; // Timeout for showing the warning
  let logoutTimeout; // Timeout for auto logout
  const warningTime = 14 * 60 * 1000; // 14 minutes (warning before 5 minutes)
  const logoutTime = 15 * 60 * 1000; // 15 minutes
  let lastActivityTime = Date.now(); // Track the last activity time
  let warningShown = false; // Track if the warning has been shown

  function resetTimers() {
    const currentTime = Date.now();
    const elapsedTime = currentTime - lastActivityTime;

    if (elapsedTime >= logoutTime) {
      // If total elapsed time exceeds logout time, redirect to login
      window.location.href = "/login";
      return;
    }

    lastActivityTime = currentTime; // Update the last activity time

    clearTimeout(warningTimeout);
    clearTimeout(logoutTimeout);

    if (!warningShown) {
      // Set warning timeout only if it hasn't been shown yet
      warningTimeout = setTimeout(() => {
        warningShown = true; // Mark the warning as shown
        alert(
          "You will be logged out due to inactivity in 60 seconds. Click OK to stay logged in."
        );
        resetTimers(); // Reset timers if the user acknowledges the warning
      }, warningTime - elapsedTime);
    }

    // Set logout timeout
    logoutTimeout = setTimeout(() => {
      window.location.href = "/login"; // Redirect to login page
    }, logoutTime - elapsedTime);
  }

  // Reset timers on user activity
  ["mousemove", "keydown", "click", "scroll"].forEach((event) => {
    document.addEventListener(event, () => {
      warningShown = false; // Reset the warning flag on activity
      resetTimers();
    });
  });

  // Initialize timers
  resetTimers();
});
