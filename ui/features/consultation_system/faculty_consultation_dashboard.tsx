import React from 'react'
import { createRoot } from 'react-dom/client'
import FacultyConsultationDashboard from './react/FacultyConsultationDashboard'

// Initialize the Faculty Consultation Dashboard
const initializeFacultyConsultationDashboard = () => {
  const container = document.getElementById('faculty-consultation-dashboard-container')
  if (!container) {
    console.error('Faculty consultation dashboard container not found')
    return
  }

  const envData = window.ENV?.FACULTY_DASHBOARD
  if (!envData) {
    console.error('Faculty dashboard environment data not found')
    return
  }

  const root = createRoot(container)
  root.render(
    <FacultyConsultationDashboard
      currentUserId={envData.current_user_id}
      pendingRequests={envData.pending_requests || []}
      upcomingConsultations={envData.upcoming_consultations || []}
      statistics={envData.statistics || {}}
    />
  )
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeFacultyConsultationDashboard)
} else {
  initializeFacultyConsultationDashboard()
}

export default initializeFacultyConsultationDashboard
