import React from 'react'
import { createRoot } from 'react-dom/client'
import ConsultationRequestsList from './react/ConsultationRequests'

// Initialize the Consultation Requests List
const initializeConsultationRequests = () => {
  const container = document.getElementById('consultation-requests-container')
  if (!container) {
    console.error('Consultation requests container not found')
    return
  }

  const envData = window.ENV?.CONSULTATION_REQUESTS
  if (!envData) {
    console.error('Consultation requests environment data not found')
    return
  }

  const root = createRoot(container)
  root.render(
    <ConsultationRequestsList
      currentUserId={envData.current_user_id}
      userRole={envData.user_role}
      initialRequests={envData.requests || []}
      totalCount={envData.total_count || 0}
      currentPage={envData.current_page || 1}
      perPage={envData.per_page || 20}
      concernTypes={envData.concern_types || []}
      statuses={envData.statuses || []}
    />
  )
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeConsultationRequests)
} else {
  initializeConsultationRequests()
}

export default initializeConsultationRequests
