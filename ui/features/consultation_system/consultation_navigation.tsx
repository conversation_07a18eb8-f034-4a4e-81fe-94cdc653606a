import React from 'react'
import { createRoot } from 'react-dom/client'
import ConsultationNavigation from './react/ConsultationNavigation'

// Initialize the Consultation Navigation
const initializeConsultationNavigation = () => {
  const container = document.getElementById('consultation-navigation-container')
  if (!container) {
    console.error('Consultation navigation container not found')
    return
  }

  const envData = window.ENV?.CONSULTATION_NAVIGATION
  if (!envData) {
    console.error('Consultation navigation environment data not found')
    return
  }

  const root = createRoot(container)
  root.render(
    <ConsultationNavigation
      currentUserId={envData.current_user_id}
      userRole={envData.user_role}
      studentStats={envData.student_stats}
      facultyStats={envData.faculty_stats}
    />
  )
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeConsultationNavigation)
} else {
  initializeConsultationNavigation()
}

export default initializeConsultationNavigation
