import React from 'react'
import { createRoot } from 'react-dom/client'
import ConsultationSummariesList from './react/ConsultationSummaries'

// Initialize the Consultation Summaries List
const initializeConsultationSummaries = () => {
  const container = document.getElementById('consultation-summaries-container')
  if (!container) {
    console.error('Consultation summaries container not found')
    return
  }

  const envData = window.ENV?.CONSULTATION_SUMMARIES
  if (!envData) {
    console.error('Consultation summaries environment data not found')
    return
  }

  const root = createRoot(container)
  root.render(
    <ConsultationSummariesList
      currentUserId={envData.current_user_id}
      initialSummaries={envData.summaries || []}
      concernTypes={envData.concern_types || []}
      filters={envData.filters || {}}
    />
  )
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeConsultationSummaries)
} else {
  initializeConsultationSummaries()
}

export default initializeConsultationSummaries
