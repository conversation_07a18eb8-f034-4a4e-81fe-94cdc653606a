import React from 'react'
import { createRoot } from 'react-dom/client'
import FacultyTimeSlots from './react/FacultyTimeSlots'

// Initialize the Faculty Time Slots management interface
const initializeFacultyTimeSlots = () => {
  const container = document.getElementById('faculty-time-slots-container')
  if (!container) {
    console.error('Faculty time slots container not found')
    return
  }

  const envData = window.ENV?.FACULTY_TIME_SLOTS
  if (!envData) {
    console.error('Faculty time slots environment data not found')
    return
  }

  const root = createRoot(container)
  root.render(
    <FacultyTimeSlots
      currentUserId={envData.current_user_id}
      initialTimeSlots={envData.time_slots || []}
      daysOfWeek={envData.days_of_week || []}
    />
  )
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeFacultyTimeSlots)
} else {
  initializeFacultyTimeSlots()
}

export default initializeFacultyTimeSlots
