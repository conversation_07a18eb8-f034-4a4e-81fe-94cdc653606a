import { useState, useCallback } from 'react'
import { 
  ValidationError, 
  FormErrorState, 
  errorsToFormState, 
  clearFormErrors 
} from '../utils/errorHandling'

interface UseFormValidationOptions<T> {
  initialData: T
  validationFn: (data: T) => ValidationError[]
  onSubmit: (data: T) => Promise<void>
}

interface UseFormValidationReturn<T> {
  data: T
  errors: FormErrorState
  isSubmitting: boolean
  isValid: boolean
  hasErrors: boolean
  setData: (data: T) => void
  updateField: (field: keyof T, value: any) => void
  clearFieldError: (field: keyof T) => void
  clearAllErrors: () => void
  validate: () => boolean
  handleSubmit: (e?: React.FormEvent) => Promise<void>
  reset: () => void
}

export const useFormValidation = <T extends Record<string, any>>({
  initialData,
  validationFn,
  onSubmit
}: UseFormValidationOptions<T>): UseFormValidationReturn<T> => {
  const [data, setData] = useState<T>(initialData)
  const [errors, setErrors] = useState<FormErrorState>({})
  const [isSubmitting, setIsSubmitting] = useState(false)

  const updateField = useCallback((field: keyof T, value: any) => {
    setData(prev => ({
      ...prev,
      [field]: value
    }))
    
    // Clear error for this field when user starts typing
    if (errors[field as string]) {
      setErrors(prev => clearFormErrors(prev, field as string))
    }
  }, [errors])

  const clearFieldError = useCallback((field: keyof T) => {
    setErrors(prev => clearFormErrors(prev, field as string))
  }, [])

  const clearAllErrors = useCallback(() => {
    setErrors({})
  }, [])

  const validate = useCallback((): boolean => {
    const validationErrors = validationFn(data)
    const formErrors = errorsToFormState(validationErrors)
    setErrors(formErrors)
    return validationErrors.length === 0
  }, [data, validationFn])

  const handleSubmit = useCallback(async (e?: React.FormEvent) => {
    if (e) {
      e.preventDefault()
    }

    if (isSubmitting) {
      return
    }

    // Validate form
    if (!validate()) {
      return
    }

    try {
      setIsSubmitting(true)
      await onSubmit(data)
      // Clear form on successful submission
      setData(initialData)
      setErrors({})
    } catch (error) {
      // Error handling is done in the parent component
      console.error('Form submission error:', error)
    } finally {
      setIsSubmitting(false)
    }
  }, [data, isSubmitting, validate, onSubmit, initialData])

  const reset = useCallback(() => {
    setData(initialData)
    setErrors({})
    setIsSubmitting(false)
  }, [initialData])

  const isValid = Object.keys(errors).length === 0
  const hasErrors = Object.keys(errors).length > 0

  return {
    data,
    errors,
    isSubmitting,
    isValid,
    hasErrors,
    setData,
    updateField,
    clearFieldError,
    clearAllErrors,
    validate,
    handleSubmit,
    reset
  }
}

// Specialized hook for time slot form
export const useTimeSlotFormValidation = (
  initialData: any,
  onSubmit: (data: any) => Promise<void>
) => {
  return useFormValidation({
    initialData,
    validationFn: (data) => {
      const errors: ValidationError[] = []

      // Required field validation
      if (!data.start_time) {
        errors.push({ field: 'start_time', message: 'Start time is required' })
      }

      if (!data.end_time) {
        errors.push({ field: 'end_time', message: 'End time is required' })
      }

      if (data.is_recurring && !data.day_of_week) {
        errors.push({ field: 'day_of_week', message: 'Day of week is required for recurring slots' })
      }

      if (!data.is_recurring && !data.specific_date) {
        errors.push({ field: 'specific_date', message: 'Specific date is required for non-recurring slots' })
      }

      // Time validation
      if (data.start_time && data.end_time) {
        const startTime = new Date(`2000-01-01T${data.start_time}:00`)
        const endTime = new Date(`2000-01-01T${data.end_time}:00`)
        
        if (endTime <= startTime) {
          errors.push({ field: 'end_time', message: 'End time must be after start time' })
        }

        const durationMinutes = (endTime.getTime() - startTime.getTime()) / (1000 * 60)
        if (durationMinutes < 30) {
          errors.push({ field: 'end_time', message: 'Duration must be at least 30 minutes' })
        }

        if (durationMinutes > 480) { // 8 hours
          errors.push({ field: 'end_time', message: 'Duration cannot exceed 8 hours' })
        }
      }

      // Date validation for non-recurring slots
      if (!data.is_recurring && data.specific_date) {
        const selectedDate = new Date(data.specific_date)
        const today = new Date()
        today.setHours(0, 0, 0, 0)

        if (selectedDate < today) {
          errors.push({ field: 'specific_date', message: 'Date cannot be in the past' })
        }
      }

      return errors
    },
    onSubmit
  })
}

// Specialized hook for consultation request form
export const useConsultationRequestFormValidation = (
  initialData: any,
  onSubmit: (data: any) => Promise<void>
) => {
  return useFormValidation({
    initialData,
    validationFn: (data) => {
      const errors: ValidationError[] = []

      // Required field validation
      if (!data.faculty_time_slot_id) {
        errors.push({ field: 'faculty_time_slot_id', message: 'Please select a time slot' })
      }

      if (!data.preferred_datetime) {
        errors.push({ field: 'preferred_datetime', message: 'Please select a preferred date and time' })
      }

      if (!data.nature_of_concern) {
        errors.push({ field: 'nature_of_concern', message: 'Please select the nature of your concern' })
      }

      if (!data.description || !data.description.trim()) {
        errors.push({ field: 'description', message: 'Please provide a description of your concern' })
      }

      // Description validation
      if (data.description) {
        const trimmedDescription = data.description.trim()
        
        if (trimmedDescription.length < 20) {
          errors.push({ 
            field: 'description', 
            message: 'Description must be at least 20 characters long' 
          })
        }

        if (trimmedDescription.length > 2000) {
          errors.push({ 
            field: 'description', 
            message: 'Description cannot exceed 2000 characters' 
          })
        }

        // Check for meaningful content
        const inappropriateWords = ['test', 'testing', 'hello', 'hi', 'spam']
        if (inappropriateWords.includes(trimmedDescription.toLowerCase())) {
          errors.push({ 
            field: 'description', 
            message: 'Please provide a meaningful description of your concern' 
          })
        }
      }

      // DateTime validation
      if (data.preferred_datetime) {
        const selectedDateTime = new Date(data.preferred_datetime)
        const now = new Date()

        if (selectedDateTime <= now) {
          errors.push({ 
            field: 'preferred_datetime', 
            message: 'Consultation time must be in the future' 
          })
        }

        // Business hours validation
        const hour = selectedDateTime.getHours()
        const dayOfWeek = selectedDateTime.getDay()

        if (dayOfWeek === 0 || dayOfWeek === 6) {
          errors.push({ 
            field: 'preferred_datetime', 
            message: 'Consultations are only available on weekdays' 
          })
        }

        if (hour < 7 || hour > 20) {
          errors.push({ 
            field: 'preferred_datetime', 
            message: 'Consultations are only available during business hours (7 AM - 8 PM)' 
          })
        }
      }

      return errors
    },
    onSubmit
  })
}
