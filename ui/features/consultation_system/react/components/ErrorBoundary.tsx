import React, { Component, ErrorInfo, ReactNode } from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { Text } from '@instructure/ui-text'
import { Alert } from '@instructure/ui-alerts'
import { IconWarningLine, IconRefreshLine } from '@instructure/ui-icons'

interface Props {
  children: ReactNode
  fallback?: ReactNode
}

interface State {
  hasError: boolean
  error: Error | null
  errorInfo: ErrorInfo | null
}

class ConsultationErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
    errorInfo: null
  }

  public static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
      errorInfo: null
    }
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('Consultation system error:', error, errorInfo)
    
    this.setState({
      error,
      errorInfo
    })

    // Log error to monitoring service if available
    if (window.ENV?.SENTRY_DSN) {
      // Sentry error reporting would go here
    }
  }

  private handleRetry = () => {
    this.setState({
      hasError: false,
      error: null,
      errorInfo: null
    })
  }

  private handleReload = () => {
    window.location.reload()
  }

  public render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <div className="consultation-system">
          <View as="div" padding="large">
            <Alert variant="error" margin="0 0 large 0">
              <View as="div" textAlign="center">
                <View as="div" margin="0 0 medium 0">
                  <IconWarningLine size="large" />
                </View>
                <Heading level="h2" margin="0 0 small 0">
                  Something went wrong
                </Heading>
                <Text>
                  The consultation system encountered an unexpected error. 
                  Please try refreshing the page or contact support if the problem persists.
                </Text>
              </View>
            </Alert>

            <View as="div" textAlign="center">
              <View as="div" display="flex" gap="medium" justifyItems="center">
                <Button
                  color="primary"
                  renderIcon={IconRefreshLine}
                  onClick={this.handleRetry}
                >
                  Try Again
                </Button>
                <Button
                  onClick={this.handleReload}
                >
                  Reload Page
                </Button>
                <Button
                  href="/consultations"
                >
                  Go to Consultations Home
                </Button>
              </View>
            </View>

            {/* Error details for development */}
            {process.env.NODE_ENV === 'development' && this.state.error && (
              <View as="div" margin="large 0 0 0">
                <details style={{ marginTop: '2rem' }}>
                  <summary style={{ cursor: 'pointer', fontWeight: 'bold' }}>
                    Error Details (Development Only)
                  </summary>
                  <View as="div" background="secondary" padding="medium" borderRadius="small" margin="small 0 0 0">
                    <Text size="small" fontFamily="monospace">
                      <strong>Error:</strong> {this.state.error.message}
                    </Text>
                    <br />
                    <Text size="small" fontFamily="monospace">
                      <strong>Stack:</strong>
                      <pre style={{ whiteSpace: 'pre-wrap', fontSize: '0.8rem' }}>
                        {this.state.error.stack}
                      </pre>
                    </Text>
                    {this.state.errorInfo && (
                      <>
                        <br />
                        <Text size="small" fontFamily="monospace">
                          <strong>Component Stack:</strong>
                          <pre style={{ whiteSpace: 'pre-wrap', fontSize: '0.8rem' }}>
                            {this.state.errorInfo.componentStack}
                          </pre>
                        </Text>
                      </>
                    )}
                  </View>
                </details>
              </View>
            )}
          </View>
        </div>
      )
    }

    return this.props.children
  }
}

export default ConsultationErrorBoundary

// Higher-order component for wrapping consultation components
export const withErrorBoundary = <P extends object>(
  Component: React.ComponentType<P>,
  fallback?: ReactNode
) => {
  const WrappedComponent = (props: P) => (
    <ConsultationErrorBoundary fallback={fallback}>
      <Component {...props} />
    </ConsultationErrorBoundary>
  )

  WrappedComponent.displayName = `withErrorBoundary(${Component.displayName || Component.name})`
  
  return WrappedComponent
}

// Lightweight error fallback component
export const ConsultationErrorFallback: React.FC<{
  error?: Error
  onRetry?: () => void
}> = ({ error, onRetry }) => (
  <View as="div" textAlign="center" padding="large" background="secondary" borderRadius="medium">
    <View as="div" margin="0 0 medium 0">
      <IconWarningLine size="medium" />
    </View>
    <Heading level="h4" margin="0 0 small 0">
      Unable to load content
    </Heading>
    <Text size="small" color="secondary" margin="0 0 medium 0">
      {error?.message || 'An error occurred while loading this section.'}
    </Text>
    {onRetry && (
      <Button size="small" onClick={onRetry}>
        Try Again
      </Button>
    )}
  </View>
)
