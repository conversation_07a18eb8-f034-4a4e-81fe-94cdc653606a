import React from 'react'
import { View } from '@instructure/ui-view'
import { Spinner } from '@instructure/ui-spinner'
import { Text } from '@instructure/ui-text'

interface LoadingSpinnerProps {
  message?: string
  size?: 'x-small' | 'small' | 'medium' | 'large'
  variant?: 'default' | 'inverse'
  margin?: 'none' | 'auto' | 'xxx-small' | 'xx-small' | 'x-small' | 'small' | 'medium' | 'large' | 'x-large' | 'xx-large'
}

const LoadingSpinner: React.FC<LoadingSpinnerProps> = ({
  message = 'Loading...',
  size = 'medium',
  variant = 'default',
  margin = 'large'
}) => {
  return (
    <View as="div" textAlign="center" padding={margin as any}>
      <Spinner
        renderTitle={message}
        size={size}
        variant={variant}
        margin="0 0 small 0"
      />
      <Text size="small" color="secondary">
        {message}
      </Text>
    </View>
  )
}

export default LoadingSpinner

// Specialized loading components for different sections
export const TimeSlotLoadingSpinner: React.FC = () => (
  <LoadingSpinner message="Loading time slots..." />
)

export const ConsultationRequestLoadingSpinner: React.FC = () => (
  <LoadingSpinner message="Loading consultation requests..." />
)

export const SummariesLoadingSpinner: React.FC = () => (
  <LoadingSpinner message="Loading consultation summaries..." />
)

export const DashboardLoadingSpinner: React.FC = () => (
  <LoadingSpinner message="Loading dashboard data..." />
)

// Inline loading spinner for smaller components
export const InlineLoadingSpinner: React.FC<{ message?: string }> = ({
  message = 'Loading...'
}) => (
  <View as="div" display="inline-flex">
    <Spinner size="x-small" renderTitle={message} />
    <View as="div" margin="0 0 0 x-small">
      <Text size="small" color="secondary">
        {message}
      </Text>
    </View>
  </View>
)

// Full page loading overlay
export const FullPageLoadingSpinner: React.FC<{ message?: string }> = ({
  message = 'Loading consultation system...'
}) => (
  <View
    as="div"
    position="fixed"
    insetInlineStart="0"
    insetBlockStart="0"
    width="100%"
    height="100%"
    background="primary"
    display="flex"
    style={{
      zIndex: 9999,
      alignItems: 'center',
      justifyContent: 'center'
    }}
  >
    <View as="div" textAlign="center">
      <Spinner
        renderTitle={message}
        size="large"
        margin="0 0 medium 0"
      />
      <Text size="medium" color="secondary">
        {message}
      </Text>
    </View>
  </View>
)
