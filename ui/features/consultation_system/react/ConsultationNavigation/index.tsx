import React from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { Badge } from '@instructure/ui-badge'
import { Text } from '@instructure/ui-text'
import { Grid } from '@instructure/ui-grid'
import { 
  IconUserLine, 
  IconGroupLine, 
  IconClockLine, 
  IconCalendarMonthLine,
  IconDocumentLine,
  IconSettingsLine,
  IconDashboardLine
} from '@instructure/ui-icons'

interface StudentStats {
  total_requests: number
  pending_requests: number
  upcoming_consultations: number
}

interface FacultyStats {
  pending_requests: number
  upcoming_consultations: number
  total_time_slots: number
  available_time_slots: number
}

interface ConsultationNavigationProps {
  currentUserId: string
  userRole: 'student' | 'faculty' | 'other'
  studentStats?: StudentStats
  facultyStats?: FacultyStats
}

const ConsultationNavigation: React.FC<ConsultationNavigationProps> = ({
  currentUserId,
  userRole,
  studentStats,
  facultyStats
}) => {
  const renderStudentSection = () => (
    <View as="div" margin="0 0 large 0">
      <Heading level="h2" margin="0 0 medium 0">
        <IconUserLine /> Student Services
      </Heading>
      
      <Grid>
        <Grid.Row>
          <Grid.Col width={6}>
            <View as="div" background="primary" padding="medium" borderRadius="medium" height="100%">
              <View as="div" display="flex" alignItems="center" margin="0 0 small 0">
                <IconCalendarMonthLine size="medium" />
                <Heading level="h3" margin="0 0 0 small">
                  Request Consultation
                </Heading>
              </View>
              <Text>
                Submit a new consultation request with available faculty members. 
                Get help with academic, personal, or other concerns.
              </Text>
              <View as="div" margin="medium 0 0 0">
                <Button 
                  color="primary" 
                  href="/consultation_requests/student_form"
                  renderIcon={IconCalendarMonthLine}
                >
                  New Request
                </Button>
              </View>
            </View>
          </Grid.Col>
          
          <Grid.Col width={6}>
            <View as="div" background="primary" padding="medium" borderRadius="medium" height="100%">
              <View as="div" display="flex" alignItems="center" margin="0 0 small 0">
                <IconDocumentLine size="medium" />
                <Heading level="h3" margin="0 0 0 small">
                  My Requests
                </Heading>
                {studentStats && studentStats.pending_requests > 0 && (
                  <Badge 
                    count={studentStats.pending_requests}
                    margin="0 0 0 small"
                    type="notification"
                  />
                )}
              </View>
              <Text>
                View and manage your consultation requests. Track status updates 
                and upcoming appointments.
              </Text>
              <View as="div" margin="medium 0 0 0">
                <Button 
                  href="/consultation_requests"
                  renderIcon={IconDocumentLine}
                >
                  View Requests
                </Button>
              </View>
            </View>
          </Grid.Col>
        </Grid.Row>
      </Grid>

      {studentStats && (
        <View as="div" margin="large 0 0 0">
          <Heading level="h4" margin="0 0 small 0">
            Your Statistics
          </Heading>
          <Grid>
            <Grid.Row>
              <Grid.Col width={4}>
                <View as="div" textAlign="center" background="secondary" padding="small" borderRadius="small">
                  <Text size="x-large" weight="bold" color="brand">
                    {studentStats.total_requests}
                  </Text>
                  <Text size="small" display="block">
                    Total Requests
                  </Text>
                </View>
              </Grid.Col>
              <Grid.Col width={4}>
                <View as="div" textAlign="center" background="secondary" padding="small" borderRadius="small">
                  <Text size="x-large" weight="bold" color="warning">
                    {studentStats.pending_requests}
                  </Text>
                  <Text size="small" display="block">
                    Pending
                  </Text>
                </View>
              </Grid.Col>
              <Grid.Col width={4}>
                <View as="div" textAlign="center" background="secondary" padding="small" borderRadius="small">
                  <Text size="x-large" weight="bold" color="success">
                    {studentStats.upcoming_consultations}
                  </Text>
                  <Text size="small" display="block">
                    Upcoming
                  </Text>
                </View>
              </Grid.Col>
            </Grid.Row>
          </Grid>
        </View>
      )}
    </View>
  )

  const renderFacultySection = () => (
    <View as="div" margin="0 0 large 0">
      <Heading level="h2" margin="0 0 medium 0">
        <IconGroupLine /> Faculty Services
      </Heading>
      
      <Grid>
        <Grid.Row>
          <Grid.Col width={4}>
            <View as="div" background="primary" padding="medium" borderRadius="medium" height="100%">
              <View as="div" display="flex" alignItems="center" margin="0 0 small 0">
                <IconDashboardLine size="medium" />
                <Heading level="h4" margin="0 0 0 small">
                  Dashboard
                </Heading>
                {facultyStats && facultyStats.pending_requests > 0 && (
                  <Badge 
                    count={facultyStats.pending_requests}
                    margin="0 0 0 small"
                    type="notification"
                  />
                )}
              </View>
              <Text size="small">
                View pending requests and upcoming consultations.
              </Text>
              <View as="div" margin="small 0 0 0">
                <Button 
                  size="small"
                  href="/consultation_requests/faculty_dashboard"
                  renderIcon={IconDashboardLine}
                >
                  Dashboard
                </Button>
              </View>
            </View>
          </Grid.Col>
          
          <Grid.Col width={4}>
            <View as="div" background="primary" padding="medium" borderRadius="medium" height="100%">
              <View as="div" display="flex" alignItems="center" margin="0 0 small 0">
                <IconClockLine size="medium" />
                <Heading level="h4" margin="0 0 0 small">
                  Time Slots
                </Heading>
              </View>
              <Text size="small">
                Manage your available consultation time slots.
              </Text>
              <View as="div" margin="small 0 0 0">
                <Button 
                  size="small"
                  href="/faculty_time_slots"
                  renderIcon={IconClockLine}
                >
                  Manage Slots
                </Button>
              </View>
            </View>
          </Grid.Col>
          
          <Grid.Col width={4}>
            <View as="div" background="primary" padding="medium" borderRadius="medium" height="100%">
              <View as="div" display="flex" alignItems="center" margin="0 0 small 0">
                <IconDocumentLine size="medium" />
                <Heading level="h4" margin="0 0 0 small">
                  Summaries
                </Heading>
              </View>
              <Text size="small">
                View and manage consultation summaries and reports.
              </Text>
              <View as="div" margin="small 0 0 0">
                <Button 
                  size="small"
                  href="/consultation_summaries"
                  renderIcon={IconDocumentLine}
                >
                  View Summaries
                </Button>
              </View>
            </View>
          </Grid.Col>
        </Grid.Row>
      </Grid>

      {facultyStats && (
        <View as="div" margin="large 0 0 0">
          <Heading level="h4" margin="0 0 small 0">
            Your Statistics
          </Heading>
          <Grid>
            <Grid.Row>
              <Grid.Col width={3}>
                <View as="div" textAlign="center" background="secondary" padding="small" borderRadius="small">
                  <Text size="large" weight="bold" color="warning">
                    {facultyStats.pending_requests}
                  </Text>
                  <Text size="small" display="block">
                    Pending Requests
                  </Text>
                </View>
              </Grid.Col>
              <Grid.Col width={3}>
                <View as="div" textAlign="center" background="secondary" padding="small" borderRadius="small">
                  <Text size="large" weight="bold" color="success">
                    {facultyStats.upcoming_consultations}
                  </Text>
                  <Text size="small" display="block">
                    Upcoming
                  </Text>
                </View>
              </Grid.Col>
              <Grid.Col width={3}>
                <View as="div" textAlign="center" background="secondary" padding="small" borderRadius="small">
                  <Text size="large" weight="bold" color="brand">
                    {facultyStats.total_time_slots}
                  </Text>
                  <Text size="small" display="block">
                    Total Slots
                  </Text>
                </View>
              </Grid.Col>
              <Grid.Col width={3}>
                <View as="div" textAlign="center" background="secondary" padding="small" borderRadius="small">
                  <Text size="large" weight="bold" color="success">
                    {facultyStats.available_time_slots}
                  </Text>
                  <Text size="small" display="block">
                    Available
                  </Text>
                </View>
              </Grid.Col>
            </Grid.Row>
          </Grid>
        </View>
      )}
    </View>
  )

  return (
    <div className="consultation-system">
      <View as="div" padding="large">
        <div className="page-header">
          <Heading level="h1" margin="0 0 small 0">
            <IconSettingsLine /> Consultation System
          </Heading>
          <Text>
            Welcome to the consultation system. Here you can request consultations with faculty members, 
            manage your appointments, and track your consultation history.
          </Text>
        </div>

        {userRole === 'student' && renderStudentSection()}
        {userRole === 'faculty' && renderFacultySection()}
        
        {userRole === 'other' && (
          <View as="div" textAlign="center" padding="x-large">
            <Heading level="h3" margin="0 0 small 0">
              Access Restricted
            </Heading>
            <Text>
              The consultation system is only available to students and faculty members. 
              Please contact your administrator if you believe you should have access.
            </Text>
          </View>
        )}

        <View as="div" margin="x-large 0 0 0" background="secondary" padding="medium" borderRadius="medium">
          <Heading level="h4" margin="0 0 small 0">
            Need Help?
          </Heading>
          <Text>
            If you have questions about using the consultation system or need technical support, 
            please contact the IT Help Desk or your academic advisor.
          </Text>
        </View>
      </View>
    </div>
  )
}

export default ConsultationNavigation
