import React, { useState } from 'react'
import { Modal } from '@instructure/ui-modal'
import { Heading } from '@instructure/ui-heading'
import { View } from '@instructure/ui-view'
import { Text } from '@instructure/ui-text'
import { Button } from '@instructure/ui-buttons'
import { Flex } from '@instructure/ui-flex'
import { Badge } from '@instructure/ui-badge'
import { Spinner } from '@instructure/ui-spinner'
import { TextArea } from '@instructure/ui-text-area'
import { IconUserLine, IconCalendarMonthLine, IconClockLine, IconCheckMarkLine, IconXLine } from '@instructure/ui-icons'
import type { ConsultationRequest, FacultyTimeSlot } from '../types'

interface ConsultationRequestDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  consultationRequests: ConsultationRequest[]
  timeSlot: FacultyTimeSlot | null
  datetime: string
  loading: boolean
  onReschedule?: (requestId: string) => void
  onApprove?: (requestId: string, comment?: string) => Promise<void>
  onDecline?: (requestId: string, comment: string) => Promise<void>
}

const ConsultationRequestDetailsModal: React.FC<ConsultationRequestDetailsModalProps> = ({
  isOpen,
  onClose,
  consultationRequests,
  timeSlot,
  datetime,
  loading,
  onReschedule,
  onApprove,
  onDecline
}) => {
  const [selectedRequest, setSelectedRequest] = useState<ConsultationRequest | null>(null)
  const [actionModalType, setActionModalType] = useState<'approve' | 'decline' | null>(null)
  const [comment, setComment] = useState('')
  const [submitting, setSubmitting] = useState(false)

  const formatDateTime = (dateTimeString: string) => {
    try {
      const date = new Date(dateTimeString)
      return date.toLocaleDateString('en-US', {
        weekday: 'long',
        year: 'numeric',
        month: 'long',
        day: 'numeric',
        hour: 'numeric',
        minute: '2-digit',
        hour12: true
      })
    } catch {
      return dateTimeString
    }
  }

  const getStatusBadgeProps = (status: string) => {
    switch (status) {
      case 'pending':
        return { type: 'notification' as const, text: 'Pending' }
      case 'declined':
        return { type: 'danger' as const, text: 'Declined' }
      case 'approved':
        return { type: 'success' as const, text: 'Approved' }
      default:
        return { type: 'secondary' as const, text: status }
    }
  }

  const handleOpenActionModal = (request: ConsultationRequest, type: 'approve' | 'decline') => {
    setSelectedRequest(request)
    setActionModalType(type)
    setComment('')
  }

  const handleCloseActionModal = () => {
    setSelectedRequest(null)
    setActionModalType(null)
    setComment('')
  }

  const handleSubmitAction = async () => {
    if (!selectedRequest) return

    try {
      setSubmitting(true)

      if (actionModalType === 'approve' && onApprove) {
        await onApprove(selectedRequest.id, comment || undefined)
      } else if (actionModalType === 'decline' && onDecline) {
        if (!comment.trim()) {
          alert('Please provide a reason for declining this request.')
          return
        }
        await onDecline(selectedRequest.id, comment)
      }

      handleCloseActionModal()
    } catch (error) {
      // Error handling is done in parent component
    } finally {
      setSubmitting(false)
    }
  }

  const renderRequestCard = (request: ConsultationRequest) => {
    const badgeProps = getStatusBadgeProps(request.status)
    
    return (
      <View
        key={request.id}
        as="div"
        background="secondary"
        padding="medium"
        borderRadius="medium"
        borderWidth="small"
        borderColor="brand"
        margin="0 0 medium 0"
      >
        <Flex direction="column" gap="small">
          <Flex justifyItems="space-between" alignItems="start">
            <View as="div">
              <Flex alignItems="center" gap="x-small" margin="0 0 x-small 0">
                <IconUserLine size="x-small" />
                <Text weight="bold" size="medium">
                  {request.student_name}
                </Text>
                <Badge {...badgeProps} />
              </Flex>
              <Text size="small" color="secondary">
                Student ID: {request.student_id}
              </Text>
              {request.student_department && (
                <Text size="small" color="secondary" display="block">
                  Department: {request.student_department}
                </Text>
              )}
            </View>
          </Flex>

          <View as="div">
            <Flex alignItems="center" gap="x-small" margin="0 0 x-small 0">
              <IconCalendarMonthLine size="x-small" />
              <Text weight="bold" size="small">Requested Time:</Text>
            </Flex>
            <Text size="small">{formatDateTime(request.preferred_datetime)}</Text>
          </View>

          <View as="div">
            <Text weight="bold" size="small" display="block" margin="0 0 x-small 0">
              Nature of Concern:
            </Text>
            <Text size="small">{request.concern_type_display}</Text>
          </View>

          <View as="div">
            <Text weight="bold" size="small" display="block" margin="0 0 x-small 0">
              Description:
            </Text>
            <Text size="small">{request.description}</Text>
          </View>

          {request.faculty_comment && (
            <View as="div">
              <Text weight="bold" size="small" display="block" margin="0 0 x-small 0">
                Faculty Comment:
              </Text>
              <Text size="small">{request.faculty_comment}</Text>
            </View>
          )}

          <View as="div">
            <Text size="x-small" color="secondary">
              Submitted: {new Date(request.created_at).toLocaleDateString('en-US', {
                month: 'short',
                day: 'numeric',
                year: 'numeric',
                hour: 'numeric',
                minute: '2-digit'
              })}
            </Text>
          </View>

          {/* Action buttons for pending requests */}
          {request.status === 'pending' && (
            <View as="div" margin="small 0 0 0">
              <Flex gap="small">
                {onApprove && (
                  <Button
                    color="success"
                    size="small"
                    renderIcon={IconCheckMarkLine}
                    onClick={() => handleOpenActionModal(request, 'approve')}
                    disabled={loading || submitting}
                  >
                    Approve
                  </Button>
                )}
                {onDecline && (
                  <Button
                    color="danger"
                    size="small"
                    renderIcon={IconXLine}
                    onClick={() => handleOpenActionModal(request, 'decline')}
                    disabled={loading || submitting}
                  >
                    Decline
                  </Button>
                )}
                {onReschedule && (
                  <Button
                    size="small"
                    onClick={() => onReschedule(request.id)}
                    disabled={loading || submitting}
                  >
                    Reschedule
                  </Button>
                )}
              </Flex>
            </View>
          )}
        </Flex>
      </View>
    )
  }

  const modalTitle = consultationRequests.length > 1
    ? `Consultation Requests (${consultationRequests.length})`
    : 'Consultation Request Details'

  // Don't render if timeSlot is null
  if (!timeSlot) {
    return null
  }

  return (
    <Modal
      open={isOpen}
      onDismiss={onClose}
      size="medium"
      label={modalTitle}
      className="consultation-modal"
    >
      <Modal.Header>
        <Heading level="h2">
          {modalTitle}
        </Heading>
      </Modal.Header>

      <Modal.Body>
        {loading ? (
          <View as="div" className="modal-loading">
            <Spinner renderTitle="Loading consultation requests..." />
            <Text className="loading-text">Loading consultation requests...</Text>
          </View>
        ) : (
          <View as="div">
            <View as="div" className="time-slot-info">
              <div className="slot-title">
                <IconClockLine size="small" />
                <Text weight="bold">Time Slot</Text>
              </div>
              <div className="slot-details">
                {timeSlot.is_recurring
                  ? `${timeSlot.day_of_week} ${timeSlot.start_time} - ${timeSlot.end_time}`
                  : `${timeSlot.specific_date} ${timeSlot.start_time} - ${timeSlot.end_time}`
                }
              </div>
            </View>

            {consultationRequests.length === 0 ? (
              <View as="div" className="empty-requests">
                <div className="empty-icon">📋</div>
                <div className="empty-message">No consultation requests found for this time slot.</div>
              </View>
            ) : (
              <View as="div">
                {consultationRequests.map(renderRequestCard)}
              </View>
            )}
          </View>
        )}
      </Modal.Body>

      <Modal.Footer>
        <Button onClick={onClose}>
          Close
        </Button>
      </Modal.Footer>

      {/* Action Modal for Approve/Decline */}
      <Modal
        open={actionModalType !== null}
        onDismiss={handleCloseActionModal}
        size="medium"
        label={actionModalType === 'approve' ? 'Approve Consultation Request' : 'Decline Consultation Request'}
      >
        <Modal.Header>
          <Heading level="h2">
            {actionModalType === 'approve' ? 'Approve' : 'Decline'} Consultation Request
          </Heading>
        </Modal.Header>

        <Modal.Body>
          {selectedRequest && (
            <View as="div">
              <View as="div" margin="0 0 medium 0">
                <Text weight="bold">Student:</Text> {selectedRequest.student_name} ({selectedRequest.student_id})
              </View>
              <View as="div" margin="0 0 medium 0">
                <Text weight="bold">Requested Time:</Text> {formatDateTime(selectedRequest.preferred_datetime)}
              </View>
              <View as="div" margin="0 0 medium 0">
                <Text weight="bold">Concern Type:</Text> {selectedRequest.concern_type_display}
              </View>
              <View as="div" margin="0 0 medium 0">
                <Text weight="bold">Description:</Text> {selectedRequest.description}
              </View>

              <TextArea
                label={actionModalType === 'approve' ? 'Optional Comment' : 'Reason for Declining (Required)'}
                value={comment}
                onChange={(e) => setComment(e.target.value)}
                placeholder={
                  actionModalType === 'approve'
                    ? 'Add any additional notes for the student (optional)...'
                    : 'Please provide a reason for declining this request...'
                }
                required={actionModalType === 'decline'}
                resize="vertical"
                height="100px"
              />
            </View>
          )}
        </Modal.Body>

        <Modal.Footer>
          <Button onClick={handleCloseActionModal} disabled={submitting}>
            Cancel
          </Button>
          <Button
            color={actionModalType === 'approve' ? 'success' : 'danger'}
            onClick={handleSubmitAction}
            disabled={submitting || (actionModalType === 'decline' && !comment.trim())}
            margin="0 0 0 x-small"
          >
            {submitting ? 'Processing...' : (actionModalType === 'approve' ? 'Approve Request' : 'Decline Request')}
          </Button>
        </Modal.Footer>
      </Modal>
    </Modal>
  )
}

export default ConsultationRequestDetailsModal
