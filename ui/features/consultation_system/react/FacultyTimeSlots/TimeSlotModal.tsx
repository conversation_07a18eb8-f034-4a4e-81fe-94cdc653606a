import React from 'react'
import { Modal } from '@instructure/ui-modal'
import { Heading } from '@instructure/ui-heading'
import TimeSlotForm from './TimeSlotForm'
import type { FacultyTimeSlot, TimeSlotFormData } from '../types'

interface TimeSlotModalProps {
  isOpen: boolean
  onClose: () => void
  daysOfWeek: string[]
  initialData?: FacultyTimeSlot | null
  prefilledData?: Partial<TimeSlotFormData> | null
  onSubmit: (data: TimeSlotFormData) => Promise<void>
  loading: boolean
}

const TimeSlotModal: React.FC<TimeSlotModalProps> = ({
  isOpen,
  onClose,
  daysOfWeek,
  initialData,
  prefilledData,
  onSubmit,
  loading
}) => {
  const modalTitle = initialData ? 'Edit Time Slot' : 'Add New Time Slot'

  return (
    <Modal
      open={isOpen}
      onDismiss={onClose}
      size="medium"
      label={modalTitle}
      className="time-slot-modal"
    >
      <Modal.Header>
        <Heading level="h2">
          {modalTitle}
        </Heading>
      </Modal.Header>

      <Modal.Body>
        <TimeSlotForm
          daysOfWeek={daysOfWeek}
          initialData={initialData}
          prefilledData={prefilledData}
          onSubmit={onSubmit}
          onCancel={onClose}
          loading={loading}
        />
      </Modal.Body>
    </Modal>
  )
}

export default TimeSlotModal
