// Error handling utilities for the consultation system

export interface ValidationError {
  field: string
  message: string
}

export interface ApiErrorResponse {
  error?: string
  errors?: string[]
  message?: string
  status?: number
}

export class ConsultationError extends Error {
  public field?: string
  public code?: string
  public status?: number

  constructor(message: string, field?: string, code?: string, status?: number) {
    super(message)
    this.name = 'ConsultationError'
    this.field = field
    this.code = code
    this.status = status
  }
}

// Form validation utilities
export const validateTimeSlotForm = (data: {
  start_time: string
  end_time: string
  day_of_week: string
  is_recurring: boolean
  specific_date: string
  is_available: boolean
  notes: string
}): ValidationError[] => {
  const errors: ValidationError[] = []

  // Required field validation
  if (!data.start_time) {
    errors.push({ field: 'start_time', message: 'Start time is required' })
  }

  if (!data.end_time) {
    errors.push({ field: 'end_time', message: 'End time is required' })
  }

  if (data.is_recurring && !data.day_of_week) {
    errors.push({ field: 'day_of_week', message: 'Day of week is required for recurring slots' })
  }

  if (!data.is_recurring && !data.specific_date) {
    errors.push({ field: 'specific_date', message: 'Specific date is required for non-recurring slots' })
  }

  // Time validation
  if (data.start_time && data.end_time) {
    const startTime = new Date(`2000-01-01T${data.start_time}:00`)
    const endTime = new Date(`2000-01-01T${data.end_time}:00`)
    
    if (endTime <= startTime) {
      errors.push({ field: 'end_time', message: 'End time must be after start time' })
    }

    const durationHours = (endTime.getTime() - startTime.getTime()) / (1000 * 60 * 60)
    if (durationHours < 0.5) {
      errors.push({ field: 'end_time', message: 'Duration must be at least 30 minutes' })
    }

    if (durationHours > 8) {
      errors.push({ field: 'end_time', message: 'Duration cannot exceed 8 hours' })
    }

    // Business hours validation
    const startHour = startTime.getHours()
    const endHour = endTime.getHours()

    if (startHour < 6 || startHour > 22) {
      errors.push({ field: 'start_time', message: 'Start time should be between 6 AM and 10 PM' })
    }

    if (endHour < 6 || endHour > 23) {
      errors.push({ field: 'end_time', message: 'End time should be between 6 AM and 11 PM' })
    }
  }

  // Date validation for non-recurring slots
  if (!data.is_recurring && data.specific_date) {
    const selectedDate = new Date(data.specific_date)
    const today = new Date()
    today.setHours(0, 0, 0, 0)

    if (selectedDate < today) {
      errors.push({ field: 'specific_date', message: 'Date cannot be in the past' })
    }

    const oneYearFromNow = new Date()
    oneYearFromNow.setFullYear(oneYearFromNow.getFullYear() + 1)

    if (selectedDate > oneYearFromNow) {
      errors.push({ field: 'specific_date', message: 'Date cannot be more than 1 year in the future' })
    }
  }

  // Notes validation
  if (data.notes && data.notes.length > 500) {
    errors.push({ field: 'notes', message: 'Notes cannot exceed 500 characters' })
  }

  return errors
}

export const validateConsultationRequestForm = (data: {
  faculty_time_slot_id: string
  preferred_datetime: string
  description: string
  nature_of_concern: string
}): ValidationError[] => {
  const errors: ValidationError[] = []

  // Required field validation
  if (!data.faculty_time_slot_id) {
    errors.push({ field: 'faculty_time_slot_id', message: 'Please select a time slot' })
  }

  if (!data.preferred_datetime) {
    errors.push({ field: 'preferred_datetime', message: 'Please select a preferred date and time' })
  }

  if (!data.nature_of_concern) {
    errors.push({ field: 'nature_of_concern', message: 'Please select the nature of your concern' })
  }

  if (!data.description || !data.description.trim()) {
    errors.push({ field: 'description', message: 'Please provide a description of your concern' })
  }

  // Description validation
  if (data.description) {
    const trimmedDescription = data.description.trim()
    
    if (trimmedDescription.length < 20) {
      errors.push({ 
        field: 'description', 
        message: 'Description must be at least 20 characters long' 
      })
    }

    if (trimmedDescription.length > 2000) {
      errors.push({ 
        field: 'description', 
        message: 'Description cannot exceed 2000 characters' 
      })
    }

    // Check for meaningful content
    const inappropriateWords = ['test', 'testing', 'hello', 'hi', 'spam']
    if (inappropriateWords.includes(trimmedDescription.toLowerCase())) {
      errors.push({ 
        field: 'description', 
        message: 'Please provide a meaningful description of your concern' 
      })
    }
  }

  // DateTime validation
  if (data.preferred_datetime) {
    const selectedDateTime = new Date(data.preferred_datetime)
    const now = new Date()

    if (selectedDateTime <= now) {
      errors.push({ 
        field: 'preferred_datetime', 
        message: 'Consultation time must be in the future' 
      })
    }

    // Business hours validation
    const hour = selectedDateTime.getHours()
    const dayOfWeek = selectedDateTime.getDay()

    if (dayOfWeek === 0 || dayOfWeek === 6) {
      errors.push({ 
        field: 'preferred_datetime', 
        message: 'Consultations are only available on weekdays' 
      })
    }

    if (hour < 7 || hour > 20) {
      errors.push({ 
        field: 'preferred_datetime', 
        message: 'Consultations are only available during business hours (7 AM - 8 PM)' 
      })
    }
  }

  return errors
}

// API error handling
export const handleApiError = (error: any): ConsultationError => {
  if (error.response) {
    // Server responded with error status
    const { data, status } = error.response
    
    if (data.errors && Array.isArray(data.errors)) {
      return new ConsultationError(data.errors.join(', '), undefined, 'VALIDATION_ERROR', status)
    }
    
    if (data.error) {
      return new ConsultationError(data.error, undefined, 'API_ERROR', status)
    }
    
    if (data.message) {
      return new ConsultationError(data.message, undefined, 'API_ERROR', status)
    }
    
    return new ConsultationError(`Server error: ${status}`, undefined, 'SERVER_ERROR', status)
  }
  
  if (error.request) {
    // Network error
    return new ConsultationError('Network error. Please check your connection.', undefined, 'NETWORK_ERROR')
  }
  
  // Other error
  return new ConsultationError(error.message || 'An unexpected error occurred', undefined, 'UNKNOWN_ERROR')
}

// User-friendly error messages
export const getErrorMessage = (error: ConsultationError): string => {
  switch (error.code) {
    case 'VALIDATION_ERROR':
      return error.message
    case 'NETWORK_ERROR':
      return 'Unable to connect to the server. Please check your internet connection and try again.'
    case 'SERVER_ERROR':
      if (error.status === 500) {
        return 'A server error occurred. Please try again later or contact support if the problem persists.'
      }
      if (error.status === 403) {
        return 'You do not have permission to perform this action.'
      }
      if (error.status === 404) {
        return 'The requested resource was not found.'
      }
      return error.message
    case 'UNKNOWN_ERROR':
    default:
      return 'An unexpected error occurred. Please try again.'
  }
}

// Form error state management
export interface FormErrorState {
  [field: string]: string
}

export const errorsToFormState = (errors: ValidationError[]): FormErrorState => {
  const formErrors: FormErrorState = {}
  errors.forEach(error => {
    formErrors[error.field] = error.message
  })
  return formErrors
}

export const clearFormErrors = (errors: FormErrorState, field: string): FormErrorState => {
  const newErrors = { ...errors }
  delete newErrors[field]
  return newErrors
}

// Retry logic for API calls
export const withRetry = async <T>(
  fn: () => Promise<T>,
  maxRetries: number = 3,
  delay: number = 1000
): Promise<T> => {
  let lastError: Error

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      return await fn()
    } catch (error) {
      lastError = error as Error
      
      // Don't retry on validation errors or client errors
      if (error instanceof ConsultationError && error.status && error.status < 500) {
        throw error
      }
      
      if (attempt === maxRetries) {
        throw lastError
      }
      
      // Wait before retrying
      await new Promise(resolve => setTimeout(resolve, delay * attempt))
    }
  }
  
  throw lastError!
}
