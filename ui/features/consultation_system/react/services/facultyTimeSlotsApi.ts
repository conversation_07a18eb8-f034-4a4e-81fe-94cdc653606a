import doFetchApi from '@canvas/do-fetch-api-effect'
import type {
  FacultyTimeSlot,
  TimeSlotFormData,
  TimeSlotResponse,
  AvailableDate,
  AvailableDateTime,
  TimeSlotConsultationRequestsResponse,
  ApiError
} from '../types'

const API_BASE = '/faculty_time_slots'

export const fetchTimeSlots = async (): Promise<TimeSlotResponse> => {
  try {
    const { json } = await doFetchApi({
      path: API_BASE,
      method: 'GET'
    })
    return json as TimeSlotResponse
  } catch (error: any) {
    throw new ApiError(error.message || 'Failed to fetch time slots')
  }
}

export const createTimeSlot = async (data: TimeSlotFormData): Promise<FacultyTimeSlot> => {
  try {
    const { json } = await doFetchApi({
      path: API_BASE,
      method: 'POST',
      body: {
        faculty_time_slot: data
      }
    })
    return json as FacultyTimeSlot
  } catch (error: any) {
    const errorMessage = error.response?.errors?.join(', ') || error.message || 'Failed to create time slot'
    throw new ApiError(errorMessage)
  }
}

export const updateTimeSlot = async (id: string, data: TimeSlotFormData): Promise<FacultyTimeSlot> => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/${id}`,
      method: 'PATCH',
      body: {
        faculty_time_slot: data
      }
    })
    return json as FacultyTimeSlot
  } catch (error: any) {
    const errorMessage = error.response?.errors?.join(', ') || error.message || 'Failed to update time slot'
    throw new ApiError(errorMessage)
  }
}

export const deleteTimeSlot = async (id: string): Promise<void> => {
  try {
    await doFetchApi({
      path: `${API_BASE}/${id}`,
      method: 'DELETE'
    })
  } catch (error: any) {
    const errorMessage = error.response?.error || error.message || 'Failed to delete time slot'
    throw new ApiError(errorMessage)
  }
}

export const fetchAvailableDates = async (startDate: string, endDate: string, facultyId?: string): Promise<AvailableDate[]> => {
  try {
    const params: any = {
      start_date: startDate,
      end_date: endDate
    }

    if (facultyId) {
      params.faculty_id = facultyId
    }

    const { json } = await doFetchApi({
      path: `${API_BASE}/available_dates`,
      method: 'GET',
      params
    })
    return json.available_dates as AvailableDate[]
  } catch (error: any) {
    throw new ApiError(error.message || 'Failed to fetch available dates')
  }
}

export const fetchAvailableTimes = async (date: string, facultyId?: string): Promise<AvailableDateTime[]> => {
  try {
    const params: any = {
      date: date
    }

    if (facultyId) {
      params.faculty_id = facultyId
    }

    const { json } = await doFetchApi({
      path: `${API_BASE}/available_times`,
      method: 'GET',
      params
    })
    return json.available_times as AvailableDateTime[]
  } catch (error: any) {
    throw new ApiError(error.message || 'Failed to fetch available times')
  }
}

export const fetchTimeSlotConsultationRequests = async (timeSlotId: string, datetime: string) => {
  try {
    const { json } = await doFetchApi({
      path: `${API_BASE}/${timeSlotId}/consultation_requests`,
      method: 'GET',
      params: {
        datetime: datetime
      }
    })
    return {
      data: json as TimeSlotConsultationRequestsResponse,
      hasError: false
    }
  } catch (error: any) {
    return {
      hasError: true,
      errorMessage: error.message || 'Failed to fetch consultation requests for time slot'
    }
  }
}

// Helper class for API errors
class ApiError extends Error {
  public status?: number
  public errors?: string[]

  constructor(message: string, status?: number, errors?: string[]) {
    super(message)
    this.name = 'ApiError'
    this.status = status
    this.errors = errors
  }
}
