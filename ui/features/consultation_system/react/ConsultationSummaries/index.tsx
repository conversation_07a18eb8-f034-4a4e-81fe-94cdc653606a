import React, { useState, useEffect } from 'react'
import { View } from '@instructure/ui-view'
import { Heading } from '@instructure/ui-heading'
import { Button } from '@instructure/ui-buttons'
import { Alert } from '@instructure/ui-alerts'
import { Spinner } from '@instructure/ui-spinner'
import { Tabs } from '@instructure/ui-tabs'
import { IconDocumentLine, IconDashboardLine, IconDownloadLine } from '@instructure/ui-icons'
import SummariesList from './SummariesList'
import SummariesFilters from './SummariesFilters'
import SummariesDashboard from './SummariesDashboard'
import { fetchConsultationSummaries, fetchSummariesDashboard } from '../services/consultationSummariesApi'
import type { ConsultationSummary, ConsultationFilters, ConsultationStatistics } from '../types'

interface ConsultationSummariesProps {
  currentUserId: string
  initialSummaries?: ConsultationSummary[]
  concernTypes: string[]
  filters?: ConsultationFilters
}

const ConsultationSummaries: React.FC<ConsultationSummariesProps> = ({
  currentUserId,
  initialSummaries = [],
  concernTypes,
  filters: initialFilters = {}
}) => {
  const [summaries, setSummaries] = useState<ConsultationSummary[]>(initialSummaries)
  const [dashboardData, setDashboardData] = useState<any>(null)
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [success, setSuccess] = useState<string | null>(null)
  const [selectedTab, setSelectedTab] = useState('summaries')
  const [filters, setFilters] = useState<ConsultationFilters>(initialFilters)
  const [totalCount, setTotalCount] = useState(0)

  useEffect(() => {
    if (initialSummaries.length === 0) {
      loadSummaries()
    }
  }, [])

  useEffect(() => {
    if (selectedTab === 'dashboard' && !dashboardData) {
      loadDashboardData()
    }
  }, [selectedTab])

  const loadSummaries = async (newFilters?: ConsultationFilters) => {
    try {
      setLoading(true)
      setError(null)
      const filtersToUse = newFilters || filters
      const response = await fetchConsultationSummaries(filtersToUse)
      setSummaries(response.summaries)
      setTotalCount(response.total_count)
    } catch (err: any) {
      setError('Failed to load consultation summaries. Please try again.')
      console.error('Error loading summaries:', err)
    } finally {
      setLoading(false)
    }
  }

  const loadDashboardData = async () => {
    try {
      setLoading(true)
      setError(null)
      const data = await fetchSummariesDashboard()
      setDashboardData(data)
    } catch (err: any) {
      setError('Failed to load dashboard data. Please try again.')
      console.error('Error loading dashboard data:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleFiltersChange = (newFilters: ConsultationFilters) => {
    setFilters(newFilters)
    loadSummaries(newFilters)
  }

  const handleExportCSV = () => {
    const params = new URLSearchParams()
    Object.entries(filters).forEach(([key, value]) => {
      if (value) params.append(key, value.toString())
    })
    
    window.location.href = `/consultation_summaries.csv?${params.toString()}`
  }

  const clearMessages = () => {
    setError(null)
    setSuccess(null)
  }

  return (
    <div className="consultation-system">
      <View as="div" padding="large">
        <div className="page-header">
          <Heading level="h1" margin="0 0 small 0">
            <IconDocumentLine /> Consultation Summaries
          </Heading>
          <p>View and manage completed consultation summaries, track outcomes, and generate reports.</p>
        </div>

        {error && (
          <Alert variant="error" margin="0 0 medium 0" onDismiss={clearMessages}>
            {error}
          </Alert>
        )}

        {success && (
          <Alert variant="success" margin="0 0 medium 0" onDismiss={clearMessages}>
            {success}
          </Alert>
        )}

        <View as="div" margin="0 0 large 0">
          <Tabs
            variant="secondary"
            onRequestTabChange={(event, { index }) => {
              const tabs = ['summaries', 'dashboard']
              setSelectedTab(tabs[index])
            }}
          >
            <Tabs.Panel
              id="summaries-tab"
              renderTitle={() => (
                <View as="div" display="flex" alignItems="center">
                  <IconDocumentLine size="x-small" />
                  <span style={{ marginLeft: '0.5rem' }}>All Summaries</span>
                  {totalCount > 0 && (
                    <span style={{ marginLeft: '0.5rem', fontSize: '0.8rem', color: '#666' }}>
                      ({totalCount})
                    </span>
                  )}
                </View>
              )}
              isSelected={selectedTab === 'summaries'}
            >
              <View as="div">
                <View as="div" display="flex" justifyItems="space-between" alignItems="center" margin="0 0 medium 0">
                  <View as="div">
                    <Heading level="h3" margin="0">
                      Consultation Summaries
                    </Heading>
                  </View>
                  <View as="div" display="flex" gap="small">
                    <Button
                      renderIcon={IconDownloadLine}
                      onClick={handleExportCSV}
                      disabled={summaries.length === 0}
                    >
                      Export CSV
                    </Button>
                    <Button
                      onClick={() => loadSummaries()}
                      disabled={loading}
                    >
                      Refresh
                    </Button>
                  </View>
                </View>

                <SummariesFilters
                  filters={filters}
                  concernTypes={concernTypes}
                  onFiltersChange={handleFiltersChange}
                  loading={loading}
                />

                {loading && selectedTab === 'summaries' ? (
                  <View as="div" textAlign="center" padding="large">
                    <Spinner renderTitle="Loading consultation summaries..." />
                  </View>
                ) : (
                  <SummariesList
                    summaries={summaries}
                    loading={loading}
                    onRefresh={() => loadSummaries()}
                  />
                )}
              </View>
            </Tabs.Panel>

            <Tabs.Panel
              id="dashboard-tab"
              renderTitle={() => (
                <View as="div" display="flex" alignItems="center">
                  <IconDashboardLine size="x-small" />
                  <span style={{ marginLeft: '0.5rem' }}>Dashboard</span>
                </View>
              )}
              isSelected={selectedTab === 'dashboard'}
            >
              {loading && selectedTab === 'dashboard' ? (
                <View as="div" textAlign="center" padding="large">
                  <Spinner renderTitle="Loading dashboard data..." />
                </View>
              ) : (
                <SummariesDashboard
                  dashboardData={dashboardData}
                  onRefresh={loadDashboardData}
                />
              )}
            </Tabs.Panel>
          </Tabs>
        </View>
      </View>
    </div>
  )
}

export default ConsultationSummaries
