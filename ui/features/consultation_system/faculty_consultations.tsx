import React from 'react'
import { createRoot } from 'react-dom/client'
import FacultyConsultations from './react/FacultyConsultations'

// Initialize the Faculty Consultations page
const initializeFacultyConsultations = () => {
  const container = document.getElementById('faculty-consultations-container')
  if (!container) {
    console.error('Faculty consultations container not found')
    return
  }

  const envData = window.ENV?.FACULTY_CONSULTATIONS
  if (!envData) {
    console.error('Faculty consultations environment data not found')
    return
  }

  const root = createRoot(container)
  root.render(
    <FacultyConsultations
      currentUserId={envData.current_user_id}
      pendingRequests={envData.pending_requests || []}
      upcomingConsultations={envData.upcoming_consultations || []}
      statistics={envData.statistics || {}}
    />
  )
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
  document.addEventListener('DOMContentLoaded', initializeFacultyConsultations)
} else {
  initializeFacultyConsultations()
}

export default initializeFacultyConsultations
