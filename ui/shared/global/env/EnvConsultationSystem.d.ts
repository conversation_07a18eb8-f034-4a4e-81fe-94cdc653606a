/*
 * Copyright (C) 2024 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHANTABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

/**
 * Consultation System environment data.
 *
 * From consultation_system controllers
 */
export interface EnvConsultationSystem {
  /**
   * Student consultation form environment data
   * From ConsultationSystemController#student_form
   */
  STUDENT_CONSULTATION_FORM?: {
    current_user_id: string
    student_info: {
      name: string
      student_id: string
    }
    available_faculty: Array<{
      id: string
      name: string
      department?: string
      available_slots_count: number
    }>
    concern_types: string[]
  }

  /**
   * Faculty consultation dashboard environment data
   * From ConsultationSystemController#faculty_dashboard
   */
  FACULTY_CONSULTATION_DASHBOARD?: {
    current_user_id: string
    faculty_info: {
      name: string
      department?: string
    }
    pending_requests: Array<{
      id: string
      student_name: string
      student_id: string
      preferred_datetime: string
      formatted_datetime: string
      description: string
      nature_of_concern: string
      concern_type_display: string
      status: string
      status_display: string
      created_at: string
      can_be_approved: boolean
      can_be_declined: boolean
    }>
    upcoming_consultations: Array<{
      id: string
      student_name: string
      student_id: string
      consultation_datetime: string
      formatted_datetime: string
      description: string
      nature_of_concern: string
      status: string
    }>
    statistics: {
      total_consultations: number
      by_concern_type: Record<string, number>
      with_referrals: number
      requiring_follow_up: number
      average_per_month: number
    }
  }

  /**
   * Faculty dashboard environment data (alternative naming)
   * From ConsultationSystemController#faculty_dashboard
   */
  FACULTY_DASHBOARD?: {
    current_user_id: string
    faculty_info: {
      name: string
      department?: string
    }
    pending_requests: Array<{
      id: string
      student_name: string
      student_id: string
      preferred_datetime: string
      formatted_datetime: string
      description: string
      nature_of_concern: string
      concern_type_display: string
      status: string
      status_display: string
      created_at: string
      can_be_approved: boolean
      can_be_declined: boolean
    }>
    upcoming_consultations: Array<{
      id: string
      student_name: string
      student_id: string
      consultation_datetime: string
      formatted_datetime: string
      description: string
      nature_of_concern: string
      status: string
    }>
    statistics: {
      total_consultations: number
      by_concern_type: Record<string, number>
      with_referrals: number
      requiring_follow_up: number
      average_per_month: number
    }
  }

  /**
   * Student consultations environment data
   * From ConsultationSystemController#student_consultations
   */
  STUDENT_CONSULTATIONS?: {
    current_user_id: string
    student_info: {
      name: string
      student_id: string
    }
    recent_requests: Array<{
      id: string
      student_name: string
      student_id: string
      faculty_name: string
      preferred_datetime: string
      formatted_datetime: string
      description: string
      nature_of_concern: string
      concern_type_display: string
      status: 'pending' | 'approved' | 'declined' | 'completed' | 'cancelled'
      status_display: string
      faculty_comment?: string
      created_at: string
      updated_at: string
      can_be_approved: boolean
      can_be_declined: boolean
      can_be_completed: boolean
    }>
    upcoming_consultations: Array<{
      id: string
      faculty_name: string
      consultation_datetime: string
      formatted_datetime: string
      description: string
      nature_of_concern: string
      status: string
    }>
    statistics: {
      total_requests: number
      pending_requests: number
      completed_consultations: number
      by_concern_type: Record<string, number>
    }
  }

  /**
   * Consultation requests list environment data
   * From ConsultationSystemController#requests
   */
  CONSULTATION_REQUESTS?: {
    current_user_id: string
    user_role: 'student' | 'faculty' | 'admin'
    requests: Array<{
      id: string
      student_name: string
      student_id: string
      faculty_name: string
      preferred_datetime: string
      formatted_datetime: string
      description: string
      nature_of_concern: string
      concern_type_display: string
      status: 'pending' | 'approved' | 'declined' | 'completed' | 'cancelled'
      status_display: string
      faculty_comment?: string
      created_at: string
      updated_at: string
      can_be_approved: boolean
      can_be_declined: boolean
      can_be_completed: boolean
    }>
    concern_types: string[]
    statuses: Array<{
      value: string
      label: string
    }>
  }

  /**
   * Consultation summaries environment data
   * From ConsultationSystemController#summaries
   */
  CONSULTATION_SUMMARIES?: {
    current_user_id: string
    summaries: Array<{
      id: string
      student_name: string
      student_id: string
      consultation_date: string
      formatted_date: string
      concern_type: string
      concern_type_display: string
      description: string
      faculty_notes?: string
      outcome_summary?: string
      referral_made?: string
      follow_up_required?: string
      has_referral: boolean
      requires_follow_up: boolean
      duration_display: string
      created_at: string
      updated_at: string
    }>
    concern_types: string[]
    filters: {
      date_range?: {
        start: string
        end: string
      }
      concern_type?: string
      has_referral?: boolean
      requires_follow_up?: boolean
    }
    statistics: {
      total_consultations: number
      by_concern_type: Record<string, number>
      with_referrals: number
      requiring_follow_up: number
      average_per_month: number
    }
  }

  /**
   * Faculty time slots management environment data
   * From ConsultationSystemController#time_slots
   */
  FACULTY_TIME_SLOTS?: {
    current_user_id: string
    faculty_info: {
      name: string
      department?: string
    }
    time_slots: Array<{
      id: string
      start_time: string
      end_time: string
      day_of_week: string
      is_recurring: boolean
      specific_date?: string
      is_available: boolean
      notes?: string
      created_at: string
      updated_at: string
      pending_requests_count: number
    }>
    days_of_week: string[]
    total_count: number
    available_count: number
  }

  /**
   * Faculty consultations environment data
   * From ConsultationSystemController#faculty_consultations
   */
  FACULTY_CONSULTATIONS?: {
    current_user_id: string
    faculty_info: {
      name: string
      department?: string
    }
    pending_requests: Array<{
      id: string
      student_name: string
      student_id: string
      preferred_datetime: string
      formatted_datetime: string
      description: string
      nature_of_concern: string
      concern_type_display: string
      status: string
      status_display: string
      created_at: string
      can_be_approved: boolean
      can_be_declined: boolean
      can_be_completed: boolean
    }>
    upcoming_consultations: Array<{
      id: string
      student_name: string
      student_id: string
      consultation_datetime: string
      formatted_datetime: string
      description: string
      nature_of_concern: string
      status: string
    }>
    statistics: {
      total_consultations: number
      by_concern_type: Record<string, number>
      with_referrals: number
      requiring_follow_up: number
      average_per_month: number
    }
  }

  /**
   * Consultation navigation environment data
   * From ConsultationSystemController#navigation
   */
  CONSULTATION_NAVIGATION?: {
    current_user_id: string
    user_type: 'student' | 'faculty' | 'admin'
    student_stats?: {
      total_requests: number
      pending_requests: number
      upcoming_consultations: number
    }
    faculty_stats?: {
      pending_requests: number
      upcoming_consultations: number
      total_time_slots: number
      available_time_slots: number
    }
    admin_stats?: {
      total_faculty: number
      total_students: number
      total_consultations: number
      pending_requests: number
    }
  }

  /**
   * General consultation system configuration
   */
  CONSULTATION_CONFIG?: {
    concern_types: string[]
    max_advance_booking_days: number
    min_advance_booking_hours: number
    default_consultation_duration: number
    reminder_hours_before: number
    allow_student_cancellation: boolean
    require_faculty_approval: boolean
  }
}
