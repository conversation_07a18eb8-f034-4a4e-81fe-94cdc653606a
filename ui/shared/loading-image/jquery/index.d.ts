/*
 * Copyright (C) 2023 - present Instructure, Inc.
 *
 * This file is part of Canvas.
 *
 * Canvas is free software: you can redistribute it and/or modify it under
 * the terms of the GNU Affero General Public License as published by the Free
 * Software Foundation, version 3 of the License.
 *
 * Canvas is distributed in the hope that it will be useful, but WITHOUT ANY
 * WARRANTY; without even the implied warranty of MERCHA<PERSON>ABILITY or FITNESS FOR
 * A PARTICULAR PURPOSE. See the GNU Affero General Public License for more
 * details.
 *
 * You should have received a copy of the GNU Affero General Public License along
 * with this program. If not, see <http://www.gnu.org/licenses/>.
 */

import * as JQuery from 'jquery'

export type LoadingImage = {
  (options?: 'hide' | 'remove' | 'remove_once'): JQuery<HTMLElement>
  (
    options: 'register_image',
    image_size: 'normal' | 'small',
    imageDef: {url: string; width: number; height: number},
  ): JQuery<HTMLElement>
  (options: {
    paddingTop?: number
    image_size?: 'normal' | 'small'
    vertical?: number
    horizontal?: number
  }): J<PERSON><PERSON><PERSON><HTMLElement>
}

declare global {
  interface JQuery {
    loadingImage: LoadingImage
  }
}
