#!/usr/bin/env ruby

# Test script to check the faculty time slots API
require_relative 'config/environment'

puts "Testing Faculty Time Slots API..."

# Find a user to test with
user = User.first
if user.nil?
  puts "No users found in the database"
  exit 1
end

puts "Using user: #{user.name} (ID: #{user.id})"

# Check if there are any faculty time slots
total_slots = FacultyTimeSlot.count
puts "Total faculty time slots in database: #{total_slots}"

if total_slots == 0
  puts "No faculty time slots found. Creating some test data..."
  
  # Create a test faculty user if needed
  faculty_user = User.where(email: '<EMAIL>').first
  if faculty_user.nil?
    faculty_user = User.create!(
      name: "Test Faculty",
      email: "<EMAIL>",
      workflow_state: 'active'
    )
    puts "Created faculty user: #{faculty_user.name} (ID: #{faculty_user.id})"
  end
  
  # Create some test time slots
  %w[Monday Tuesday Wednesday Thursday Friday].each do |day|
    faculty_user.faculty_time_slots.create!(
      day_of_week: day,
      start_time: Time.parse("09:00"),
      end_time: Time.parse("10:00"),
      is_recurring: true,
      is_available: true,
      notes: "Test consultation slot"
    )
  end
  
  puts "Created #{faculty_user.faculty_time_slots.count} test time slots"
else
  puts "Found existing faculty time slots:"
  FacultyTimeSlot.limit(5).each do |slot|
    puts "  - #{slot.user.name}: #{slot.day_of_week} #{slot.start_time.strftime('%H:%M')}-#{slot.end_time.strftime('%H:%M')} (Available: #{slot.is_available})"
  end
end

# Test the API logic
puts "\nTesting API logic..."
faculty_user = User.joins(:faculty_time_slots).first
if faculty_user
  puts "Testing with faculty: #{faculty_user.name} (ID: #{faculty_user.id})"
  
  start_date = Date.today
  end_date = start_date + 7.days
  
  puts "Date range: #{start_date} to #{end_date}"
  
  # Simulate the controller logic
  recurring_slots = faculty_user.faculty_time_slots.available.recurring
  specific_date_slots = faculty_user.faculty_time_slots.available.specific_date
                               .where(specific_date: start_date..end_date)
  
  puts "Recurring slots: #{recurring_slots.count}"
  puts "Specific date slots: #{specific_date_slots.count}"
  
  formatted_slots = []
  
  # Process recurring slots
  (start_date..end_date).each do |date|
    day_name = date.strftime('%A')
    day_slots = recurring_slots.for_day(day_name)
    
    puts "#{day_name} (#{date}): #{day_slots.count} slots"
    
    day_slots.each do |slot|
      slot_time = Time.zone.local(date.year, date.month, date.day,
                                 slot.start_time.hour, slot.start_time.min)
      
      formatted_slots << {
        id: "#{slot.id}-#{slot_time.to_i}",
        datetime: slot_time.iso8601,
        formatted_time: slot_time.strftime("%I:%M %p"),
        is_available: slot.is_available,
        is_booked: false,
        faculty_id: faculty_user.id,
        faculty_time_slot_id: slot.id
      }
    end
  end
  
  puts "Total formatted slots: #{formatted_slots.count}"
  puts "Sample slots:"
  formatted_slots.first(3).each do |slot|
    puts "  - #{slot[:datetime]} (#{slot[:formatted_time]}) - Available: #{slot[:is_available]}"
  end
else
  puts "No faculty user with time slots found"
end

puts "\nTest completed!"
