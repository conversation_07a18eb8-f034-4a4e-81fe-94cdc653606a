#!/usr/bin/env ruby

# Test the time slot logic
require 'time'
require 'date'

# Simulate the database time values (stored as Time objects in UTC)
# From the console output: start=>Sat, 01 Jan 2000 14:00:00.000000000 UTC +00:00
start_time = Time.parse("2000-01-01 14:00:00 UTC")  # 2:00 PM UTC
end_time = Time.parse("2000-01-01 14:30:00 UTC")    # 2:30 PM UTC

puts "Database values:"
puts "Start time: #{start_time} (#{start_time.strftime('%H:%M')})"
puts "End time: #{end_time} (#{end_time.strftime('%H:%M')})"
puts

# Test date
test_date = Date.parse("2025-07-23")  # Wednesday
puts "Test date: #{test_date} (#{test_date.strftime('%A')})"
puts

# Extract time components
start_time_str = start_time.strftime('%H:%M')
end_time_str = end_time.strftime('%H:%M')

puts "Extracted time strings:"
puts "Start: #{start_time_str}"
puts "End: #{end_time_str}"
puts

# Simulate different timezones
timezones = ['UTC', 'America/Los_Angeles', 'America/New_York', 'Asia/Manila']

timezones.each do |tz_name|
  puts "=== Testing with timezone: #{tz_name} ==="
  
  # Set timezone (simulating Time.zone = ...)
  tz = TZInfo::Timezone.get(tz_name)
  
  # Parse in the context of the timezone
  current_time = tz.local_time(test_date.year, test_date.month, test_date.day, 
                               start_time.hour, start_time.min)
  
  puts "Generated datetime: #{current_time}"
  puts "Formatted time: #{current_time.strftime('%I:%M %p')}"
  puts "ISO8601: #{current_time.iso8601}"
  puts
end
